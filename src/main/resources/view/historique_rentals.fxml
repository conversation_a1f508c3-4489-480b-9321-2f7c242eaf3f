<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueRentalsController">
    <content>
        <VBox spacing="24.0" style="-fx-background-color: white; -fx-padding: 36; -fx-background-radius: 16;" minHeight="1200" prefHeight="1400">
            <padding><Insets bottom="200.0" /></padding>
    <Label text="Historique des Locations" style="-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" />
    <HBox spacing="16.0">
        <TextField fx:id="searchClientField" promptText="Rechercher client..." />
        <TextField fx:id="searchVehiculeField" promptText="Rechercher véhicule..." />
        <DatePicker fx:id="dateDebutFilter" promptText="Date début" />
        <DatePicker fx:id="dateFinFilter" promptText="Date fin" />
        <Button text="Filtrer" onAction="#handleFilter" />
        <Button text="Effacer" onAction="#handleClearFilters" />
    </HBox>
    <TableView fx:id="rentalHistoryTable" prefHeight="350.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 10;">
        <columns>
            <TableColumn fx:id="colId" prefWidth="50.0" text="ID" />
            <TableColumn fx:id="colClient" prefWidth="150.0" text="Client" />
            <TableColumn fx:id="colVehicule" prefWidth="150.0" text="Véhicule" />
            <TableColumn fx:id="colDateDebut" prefWidth="120.0" text="Date Début" />
            <TableColumn fx:id="colDateFin" prefWidth="120.0" text="Date Fin" />
            <TableColumn fx:id="colStatut" prefWidth="100.0" text="Statut" />
            <TableColumn fx:id="colPrix" prefWidth="100.0" text="Prix Total" />
        </columns>
    </TableView>
    <HBox spacing="20.0">
        <Label text="Détails de la location sélectionnée :" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
        <Label fx:id="detailsLabel" style="-fx-font-size: 15px; -fx-text-fill: #64748b;" text="Sélectionnez une location pour voir les détails." />
    </HBox>
        </VBox>
    </content>
</ScrollPane>