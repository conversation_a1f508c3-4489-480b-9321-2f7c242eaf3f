<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueController">
    <content>
        <VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 12;">
    <HBox alignment="CENTER_LEFT" spacing="20.0">
        <Label text="Historique de location du véhicule" style="-fx-font-size: 22px; -fx-font-weight: bold;" />
        <Region HBox.hgrow="ALWAYS" />
        <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: bold;" text="📊 Exporter" />
    </HBox>
    <GridPane hgap="10" vgap="10">
        <Label text="Dernier client:" GridPane.rowIndex="0" GridPane.columnIndex="0" />
        <Label fx:id="clientLabel" GridPane.rowIndex="0" GridPane.columnIndex="1" />
        <Label text="Date de location:" GridPane.rowIndex="1" GridPane.columnIndex="0" />
        <Label fx:id="dateLocationLabel" GridPane.rowIndex="1" GridPane.columnIndex="1" />
        <Label text="Date de retour:" GridPane.rowIndex="2" GridPane.columnIndex="0" />
        <Label fx:id="dateRetourLabel" GridPane.rowIndex="2" GridPane.columnIndex="1" />
        <Label text="Métrage au départ:" GridPane.rowIndex="3" GridPane.columnIndex="0" />
        <Label fx:id="kmDepartLabel" GridPane.rowIndex="3" GridPane.columnIndex="1" />
        <Label text="Métrage au retour:" GridPane.rowIndex="4" GridPane.columnIndex="0" />
        <Label fx:id="kmRetourLabel" GridPane.rowIndex="4" GridPane.columnIndex="1" />
        <Label text="Période de location:" GridPane.rowIndex="5" GridPane.columnIndex="0" />
        <Label fx:id="periodeLabel" GridPane.rowIndex="5" GridPane.columnIndex="1" />
        <Label text="Dommages ou problèmes:" GridPane.rowIndex="6" GridPane.columnIndex="0" />
        <Label fx:id="issuesLabel" GridPane.rowIndex="6" GridPane.columnIndex="1" />
    </GridPane>
    <Button text="Fermer" onAction="#handleClose" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 8; -fx-padding: 10; -fx-font-size: 14px;" />
        </VBox>
    </content>
</ScrollPane>