<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.VehiculeDetailController" prefWidth="400.0">
    
    <!-- Vehicle Image -->
    <VBox alignment="CENTER" spacing="10.0">
        <ImageView fx:id="vehiculeImage" fitWidth="320" fitHeight="180" style="-fx-background-color: #f1f5f9; -fx-background-radius: 8; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-border-width: 1;" />
        <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Image du véhicule" />
    </VBox>
    
    <!-- Vehicle Information -->
    <VBox spacing="15.0">
        <VBox spacing="5.0">
            <Label fx:id="marqueLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Marque" />
            <Label fx:id="modeleLabel" style="-fx-font-size: 18px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Modèle" />
        </VBox>
        
        <VBox spacing="10.0" style="-fx-background-color: #f8fafc; -fx-padding: 15; -fx-background-radius: 8;">
            <HBox alignment="CENTER_LEFT" spacing="10.0">
                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="🚗 Immatriculation:" />
                <Label fx:id="immatriculationLabel" style="-fx-font-size: 14px; -fx-text-fill: #1a3c40;" text="ABC-123" />
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="10.0">
                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="📊 État:" />
                <Label fx:id="etatLabel" style="-fx-font-size: 14px; -fx-text-fill: #10b981; -fx-font-weight: bold;" text="Disponible" />
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="10.0">
                <Label style="-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #374151;" text="💰 Prix par jour:" />
                <Label fx:id="prixLabel" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;" text="0 DH" />
            </HBox>
        </VBox>
        
        <!-- Additional Details -->
        <VBox spacing="8.0" style="-fx-background-color: #f8fafc; -fx-padding: 15; -fx-background-radius: 8;">
            <Label style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Détails supplémentaires" />
            <VBox spacing="5.0">
                <HBox alignment="CENTER_LEFT" spacing="10.0">
                    <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #64748b;" text="• Type de carburant:" />
                    <Label fx:id="carburantLabel" style="-fx-font-size: 12px; -fx-text-fill: #1a3c40;" text="Essence" />
                </HBox>
                <HBox alignment="CENTER_LEFT" spacing="10.0">
                    <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #64748b;" text="• Transmission:" />
                    <Label fx:id="transmissionLabel" style="-fx-font-size: 12px; -fx-text-fill: #1a3c40;" text="Manuelle" />
                </HBox>
                <HBox alignment="CENTER_LEFT" spacing="10.0">
                    <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #64748b;" text="• Nombre de places:" />
                    <Label fx:id="placesLabel" style="-fx-font-size: 12px; -fx-text-fill: #1a3c40;" text="5" />
                </HBox>
            </VBox>
        </VBox>
    </VBox>
    
    <!-- Action Buttons -->
    <VBox spacing="10.0">
        <Button fx:id="btnLouer" onAction="#handleLouer" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 15; -fx-font-size: 16px; -fx-font-weight: bold; -fx-font-family: 'Segoe UI';" text="🚗 Louer ce véhicule" />
        <Button fx:id="btnReserver" onAction="#handleReserver" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="📅 Réserver pour plus tard" />
        <Button fx:id="btnHistorique" onAction="#handleHistorique" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="📖 Voir historique" />
        <Button fx:id="btnDisponibilite" onAction="#handleDisponibilite" style="-fx-background-color: #22c55e; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12; -fx-font-size: 14px; -fx-font-family: 'Segoe UI';" text="✅ Voir disponibilité" />

    </VBox>
    
    <!-- Status Message -->
    <Label fx:id="statusLabel" style="-fx-text-fill: #10b981; -fx-font-size: 14px; -fx-font-family: 'Segoe UI'; -fx-alignment: center;" text="" />
</VBox> 