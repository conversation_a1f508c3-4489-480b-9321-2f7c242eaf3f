<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.PaiementController">
    <content>
        <VBox spacing="25.0" style="-fx-background-color: #f8fafc;" prefHeight="1400">
    <padding><Insets bottom="200.0" left="25.0" right="25.0" top="25.0" /></padding>
    
    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
        <VBox spacing="5.0">
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Gestion des Paiements" />
            <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Gérez tous les paiements et transactions" />
        </VBox>
        <Region HBox.hgrow="ALWAYS" />
        <HBox spacing="12.0">
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #475569; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI';" text="🔄 Actualiser" />
            <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Exporter" />
        </HBox>
    </HBox>
    
    <!-- Main Content Area -->
    <HBox spacing="20.0">
        <!-- Left Panel - Payment List -->
        <VBox prefWidth="700.0" spacing="15.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" HBox.hgrow="ALWAYS">
            <!-- Search and Filter -->
            <VBox spacing="15.0">
                <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Recherche et Filtres" />
                <HBox spacing="10.0">
                    <TextField fx:id="searchField" prefHeight="25.0" prefWidth="263.0" promptText="Rechercher par client, statut, montant..." />
                    <Button fx:id="btnSearch" onAction="#handleRechercher" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6;" text="🔍" />
                    <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 6;" text="Effacer" />
                </HBox>
            </VBox>
            
            <!-- Payment Table -->
            <VBox spacing="10.0">
                <HBox alignment="CENTER_LEFT" spacing="10.0">
                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Liste des Paiements" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Total: 0" />
                </HBox>
                <TableView fx:id="paiementTable" prefHeight="400.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 6; -fx-table-header-border-color: #e2e8f0;" VBox.vgrow="ALWAYS">
                    <columns>
                        <TableColumn fx:id="idColumn" prefWidth="50.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="ID" />
                        <TableColumn fx:id="clientColumn" prefWidth="150.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Client" />
                        <TableColumn fx:id="montantColumn" prefWidth="120.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Montant (DH)" />
                        <TableColumn fx:id="dateColumn" prefWidth="120.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Date" />
                        <TableColumn fx:id="statutColumn" prefWidth="100.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Statut" />
                        <TableColumn fx:id="methodeColumn" prefWidth="120.0" style="-fx-text-fill: black; -fx-font-weight: bold;" text="Méthode" />
                    </columns>
                </TableView>
                
                <!-- Action Buttons -->
                <HBox alignment="CENTER_RIGHT" spacing="10.0">
                    <Button fx:id="btnAddPaiement" onAction="#handleAjouter" style="-fx-background-color: #1a3c40; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="➕ Ajouter" />
                    <Button fx:id="btnUpdatePaiement" onAction="#handleModifier" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✏️ Modifier" />
                    <Button fx:id="btnDeletePaiement" onAction="#handleSupprimer" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="🗑️ Supprimer" />
                </HBox>
            </VBox>
        </VBox>
        
        <!-- Right Panel - Payment Details/Form -->
        <VBox prefWidth="400.0" spacing="15.0">
            <VBox spacing="8.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Détails du Paiement" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Client" />
                <ComboBox fx:id="txtClientForm" promptText="Sélectionner le client" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Montant (DH)" />
                <TextField fx:id="txtMontantForm" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Date" />
                <DatePicker fx:id="txtDateForm" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Statut" />
                <ComboBox fx:id="txtStatutForm" promptText="Sélectionner le statut" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Méthode de Paiement" />
                <ComboBox fx:id="txtMethodeForm" promptText="Sélectionner la méthode" />
                <HBox alignment="CENTER_RIGHT" spacing="10.0">
                    <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8 16;" text="Annuler" />
                    <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="💾 Enregistrer" />
                </HBox>
            </VBox>
        </VBox>
    </HBox>
        </VBox>
    </content>
</ScrollPane>
