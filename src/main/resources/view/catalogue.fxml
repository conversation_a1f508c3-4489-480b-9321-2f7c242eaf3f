<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.CatalogueController">
    <content>
        <VBox spacing="25.0" style="-fx-background-color: #f8fafc;" prefHeight="1200">
    <padding><Insets bottom="150.0" left="25.0" right="25.0" top="25.0" /></padding>
    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
        <VBox spacing="5.0">
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Catalogue des Véhicules" />
            <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Découvrez notre flotte de véhicules disponibles" />
        </VBox>
        <Region HBox.hgrow="ALWAYS" />
        <!-- Modern Search and Filter Controls -->
        <HBox spacing="10.0" alignment="CENTER_RIGHT">
            <TextField fx:id="searchField" promptText="🔍 Rechercher..." style="-fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 14px; -fx-min-width: 220;" />
            <Button fx:id="btnFilter" onAction="#handleFilter" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI';" text="Filtrer" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #475569; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI';" text="Actualiser" />
        </HBox>
    </HBox>
    <!-- Filter Section -->
    <VBox spacing="15.0" style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
        <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Filtres de Recherche" />
        <HBox alignment="CENTER_LEFT" spacing="15.0">
            <ComboBox fx:id="marqueFilter" promptText="Toutes les marques" style="-fx-background-radius: 6;" />
            <ComboBox fx:id="etatFilter" promptText="Tous les états" style="-fx-background-radius: 6;" />
            <TextField fx:id="prixFilter" promptText="Prix maximum" style="-fx-background-radius: 6;" />
            <TextField fx:id="searchFilterField" promptText="Rechercher par marque, modèle..." style="-fx-background-radius: 6;" />
            <Button fx:id="btnApplyFilters" onAction="#handleApplyFilters" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="Appliquer" />
            <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="Effacer" />
        </HBox>
    </VBox>
    <!-- Catalogue Content -->
    <VBox spacing="15.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" VBox.vgrow="ALWAYS">
        <HBox alignment="CENTER_LEFT" spacing="10.0">
            <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Véhicules Disponibles" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Total: 0 véhicules" />
        </HBox>
        <!-- Vehicle List -->
        <ScrollPane fitToWidth="true" hbarPolicy="NEVER" prefHeight="1763.0" prefWidth="774.0" style="-fx-background: #fff; -fx-border-color: transparent;" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS">
            <FlowPane fx:id="cataloguePane" hgap="16" vgap="16" style="-fx-background-color: #fff;">
                <padding>
                    <Insets top="16" right="16" bottom="16" left="16" />
                </padding>
            </FlowPane>
        </ScrollPane>
        <!-- Empty State -->
        <VBox fx:id="emptyState" alignment="CENTER" spacing="18.0" style="-fx-padding: 60;" visible="false">
            <Label style="-fx-font-size: 22px; -fx-text-fill: #64748b;" text="Aucun véhicule trouvé." />
        </VBox>
    </VBox>
        </VBox>
    </content>
</ScrollPane>
