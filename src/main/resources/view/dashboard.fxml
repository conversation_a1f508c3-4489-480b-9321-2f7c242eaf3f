<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>

<BorderPane prefHeight="900.0" prefWidth="1600.0" style="-fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DashboardController">

    <!-- MODERN SIDEBAR START -->
    <left>
        <VBox minHeight="-Infinity" minWidth="-Infinity" prefHeight="1223.0" prefWidth="238.0" spacing="16" style="-fx-background-color: linear-gradient(to bottom, #ffffff, #f8fafc); -fx-padding: 24 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 4, 0); -fx-border-color: #e2e8f0; -fx-border-width: 0 1 0 0;">

            <!-- Logo Section -->
            <VBox alignment="CENTER" minHeight="-Infinity" minWidth="-Infinity" prefHeight="65.0" prefWidth="216.0" spacing="16" style="-fx-padding: 0 0 20 0;">
                <HBox alignment="CENTER_LEFT" spacing="12">
                    <StackPane>
                        <Circle fill="linear-gradient(to bottom, #667eea, #764ba2)" radius="24" style="-fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 8, 0, 0, 2);" />
                        <VBox alignment="CENTER">
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="🚗" />
                        </VBox>
                    </StackPane>
                    <VBox spacing="2">
                        <Label style="-fx-text-fill: #1e293b; -fx-font-size: 20px; -fx-font-weight: 700; -fx-font-family: 'Segoe UI';" text="LocationV1" />
                        <Label style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-font-family: 'Segoe UI';" text="Tableau de bord" />
                    </VBox>
                </HBox>
                <Separator style="-fx-background-color: #e2e8f0;" />
            </VBox>

            <!-- GESTION HEADER -->
            <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px; -fx-font-weight: bold; -fx-padding: 16 0 0 0;" text="GESTION" />

            <!-- Navigation Menu -->
            <VBox spacing="4">
                <Label style="-fx-text-fill: #9ca3af; -fx-font-size: 11px; -fx-font-weight: 600; -fx-padding: 0 12 8 12;" styleClass="text-caption" text="MENU PRINCIPAL" />

                <Button fx:id="btnDashboard" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: white; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 8, 0, 0, 2);">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: white; -fx-font-size: 16px;" text="📊" />
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: 600;" text="Tableau de bord" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnClients" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="👥" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Clients" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnVehicules" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🚗" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Véhicules" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnCatalogue" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📋" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Catalogue" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnNewLocation" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: linear-gradient(to bottom, #10b981, #059669); -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: white; -fx-font-weight: 600; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 8, 0, 0, 2);">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: white; -fx-font-size: 16px;" text="➕" />
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: 600;" text="Nouvelle Location" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnLocations" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📅" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Locations" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnPaiements" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="💰" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Paiements" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnUserManagement" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="👥" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Utilisateurs" />
                        </HBox>
                    </graphic>
                </Button>

                <!-- Section Separator -->
                <VBox prefHeight="37.0" prefWidth="239.0" spacing="8" style="-fx-padding: 16 0 8 0;">
                    <Separator style="-fx-background-color: #e2e8f0;" />
                    <Label style="-fx-text-fill: #9ca3af; -fx-font-size: 11px; -fx-font-weight: 600; -fx-padding: 0 12;" styleClass="text-caption" text="HISTORIQUE" />
                </VBox>
                <Button fx:id="btnRentalHistory" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📊" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Historique Locations" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnPaymentHistory" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 12; -fx-padding: 14 16; -fx-text-fill: #64748b;" styleClass="modern-nav-button">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="💳" />
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 14px; -fx-font-weight: 500;" text="Historique Paiements" />
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <!-- USER PROFILE SECTION -->
            <VBox prefHeight="160.0" prefWidth="239.0" spacing="16" style="-fx-padding: 16 0 0 0;">
                <Separator style="-fx-background-color: #e2e8f0;" />

                <!-- User Info Card -->
                <VBox spacing="12" style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-padding: 16; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 12, 0, 0, 4);">
                    <HBox alignment="CENTER_LEFT" spacing="12">
                        <StackPane>
                            <Circle fill="rgba(255,255,255,0.2)" radius="24" />
                            <Circle fill="rgba(255,255,255,0.9)" radius="20" />
                            <Label fx:id="lblUserName" style="-fx-text-fill: #667eea; -fx-font-size: 14px; -fx-font-weight: bold;" text="AD" />
                        </StackPane>
                        <VBox spacing="2" HBox.hgrow="ALWAYS">
                            <Label style="-fx-text-fill: white; -fx-font-size: 15px; -fx-font-weight: 600;" text="Administrateur" />
                            <Label fx:id="lblUserRole" style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 12px;" text="<EMAIL>" />
                        </VBox>
                    </HBox>

                    <!-- Logout Button -->
                    <Button fx:id="btnLogout" maxWidth="Infinity" onAction="#handleLogout" style="-fx-background-color: rgba(255,255,255,0.15); -fx-text-fill: white; -fx-background-radius: 10; -fx-padding: 10 16; -fx-font-size: 13px; -fx-font-weight: 500; -fx-cursor: hand; -fx-border-color: rgba(255,255,255,0.2); -fx-border-width: 1; -fx-border-radius: 10;">
                        <graphic>
                            <HBox alignment="CENTER" spacing="8">
                                <Label style="-fx-text-fill: white; -fx-font-size: 14px;" text="🚪" />
                                <Label style="-fx-text-fill: white; -fx-font-size: 13px; -fx-font-weight: 500;" text="Déconnexion" />
                            </HBox>
                        </graphic>
                    </Button>
                </VBox>
            </VBox>
        </VBox>
    </left>

    <!-- MODERN CONTENT AREA -->
    <center>
        <StackPane fx:id="contentPane" style="-fx-background-color: #f8fafc; -fx-padding: 24;" />
    </center>

    <!-- TOP BAR (Optional - for breadcrumbs, notifications, etc.) -->
    <top>
        <HBox alignment="CENTER_LEFT" spacing="20" style="-fx-background-color: white; -fx-padding: 16 24; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1); -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;">
            <Label style="-fx-text-fill: #1e293b; -fx-font-size: 18px; -fx-font-weight: 600;" styleClass="text-heading-3" text="Tableau de Bord" />
            <Region HBox.hgrow="ALWAYS" />
            <HBox alignment="CENTER_RIGHT" spacing="12">
                <Button style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand;" text="🔔 Notifications" />
                <Button style="-fx-background-color: #f1f5f9; -fx-text-fill: #64748b; -fx-background-radius: 8; -fx-padding: 8 12; -fx-font-size: 12px; -fx-cursor: hand;" text="⚙️ Paramètres" />
            </HBox>
        </HBox>
    </top>
</BorderPane>
