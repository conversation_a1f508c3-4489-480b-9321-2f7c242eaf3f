<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.UserManagementController">
    <content>
        <VBox spacing="25.0" style="-fx-background-color: #f8fafc;" prefHeight="1200">
    <padding><Insets bottom="150.0" left="25.0" right="25.0" top="25.0" /></padding>
    
    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
        <VBox spacing="5.0">
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Gestion des Utilisateurs" />
            <Label style="-fx-font-size: 14px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Gérez les comptes utilisateurs et les permissions" />
        </VBox>
        <Region HBox.hgrow="ALWAYS" />
        <HBox spacing="12.0">
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #f1f5f9; -fx-text-fill: #475569; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI';" text="🔄 Actualiser" />
            <Button fx:id="btnExport" onAction="#handleExport" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 13px; -fx-background-radius: 8; -fx-padding: 10 16; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold;" text="📊 Exporter" />
        </HBox>
    </HBox>
    
    <!-- Main Content Area -->
    <HBox spacing="20.0">
        <!-- Left Panel - User List -->
        <VBox prefWidth="700.0" spacing="15.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" HBox.hgrow="ALWAYS">
            <!-- Search and Filter -->
            <VBox spacing="15.0">
                <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Recherche et Filtres" />
                <HBox spacing="10.0">
                    <TextField fx:id="searchField" promptText="Rechercher par nom d'utilisateur, rôle..." />
                    <Button fx:id="btnSearch" onAction="#handleSearch" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6;" text="🔍" />
                    <Button fx:id="btnClearFilters" onAction="#handleClearFilters" style="-fx-background-color: #6b7280; -fx-text-fill: white; -fx-background-radius: 6;" text="Effacer" />
                </HBox>
            </VBox>
            
            <!-- User Table -->
            <VBox spacing="10.0">
                <HBox alignment="CENTER_LEFT" spacing="10.0">
                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Liste des Utilisateurs" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Label fx:id="lblTotalCount" style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';" text="Total: 0" />
                </HBox>
                <TableView fx:id="userTable" prefHeight="400.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 6; -fx-table-header-border-color: #e2e8f0;" VBox.vgrow="ALWAYS">
                    <columns>
                        <TableColumn fx:id="idColumn" prefWidth="50.0" text="ID" style="-fx-text-fill: black; -fx-font-weight: bold;" />
                        <TableColumn fx:id="usernameColumn" prefWidth="200.0" text="Nom d'utilisateur" style="-fx-text-fill: black; -fx-font-weight: bold;" />
                        <TableColumn fx:id="roleColumn" prefWidth="150.0" text="Rôle" style="-fx-text-fill: black; -fx-font-weight: bold;" />
                        <TableColumn fx:id="emailColumn" prefWidth="200.0" text="Email" style="-fx-text-fill: black; -fx-font-weight: bold;" />
                        <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Statut" style="-fx-text-fill: black; -fx-font-weight: bold;" />
                    </columns>
                </TableView>
                
                <!-- Action Buttons -->
                <HBox alignment="CENTER_RIGHT" spacing="10.0">
                    <Button fx:id="btnAddUser" onAction="#handleSave" style="-fx-background-color: #1a3c40; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="➕ Ajouter" />
                    <Button fx:id="btnUpdateUser" onAction="#handleSave" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="✏️ Modifier" />
                    <Button fx:id="btnDeleteUser" onAction="#handleDeleteUser" style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="🗑️ Supprimer" />
                </HBox>
            </VBox>
        </VBox>
        
        <!-- Right Panel - User Details/Form -->
        <VBox prefWidth="400.0" spacing="15.0">
            <VBox spacing="8.0" style="-fx-background-color: white; -fx-padding: 25; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                <Label style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Détails de l'Utilisateur" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Nom d'utilisateur" />
                <TextField fx:id="usernameField" promptText="Entrez le nom d'utilisateur" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Mot de passe" />
                <PasswordField fx:id="passwordField" promptText="Entrez le mot de passe" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Email" />
                <TextField fx:id="emailField" promptText="Entrez l'email" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Rôle" />
                <ComboBox fx:id="roleCombo" promptText="Sélectionner le rôle" />
                <Label style="-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #3b82f6;" text="Statut" />
                <ComboBox fx:id="statusCombo" promptText="Sélectionner le statut" />
                <HBox alignment="CENTER_RIGHT" spacing="10.0">
                    <Button fx:id="btnCancel" onAction="#handleCancel" style="-fx-background-color: transparent; -fx-text-fill: #6b7280; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8 16;" text="Annuler" />
                    <Button fx:id="btnSave" onAction="#handleSave" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16;" text="💾 Enregistrer" />
                </HBox>
            </VBox>
        </VBox>
    </HBox>
        </VBox>
    </content>
</ScrollPane>