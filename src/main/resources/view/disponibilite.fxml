<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.*?>
<ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" style="-fx-background: #f8fafc; -fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DisponibiliteController">
    <content>
        <VBox spacing="20.0" style="-fx-background-color: #f8fafc; -fx-padding: 30;" prefHeight="1200">
            <padding><Insets bottom="200.0" /></padding>
            <VBox spacing="20.0" style="-fx-background-color: white; -fx-padding: 30; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);">
                <Label text="Disponibilité du véhicule" style="-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" />
                <Label fx:id="statusLabel" style="-fx-font-size: 16px; -fx-text-fill: #1a3c40;" />
                <Label fx:id="nextAvailableLabel" style="-fx-font-size: 14px; -fx-text-fill: #64748b;" />
                <Button text="Fermer" onAction="#handleClose" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 12 20; -fx-font-size: 14px; -fx-font-weight: 600;" />
            </VBox>
        </VBox>
    </content>
</ScrollPane>