<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>

<VBox spacing="24" style="-fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DashboardContentController">

    <padding><Insets bottom="24" left="24" right="24" top="24" /></padding>

    <!-- Welcome Header -->
    <VBox spacing="16" style="-fx-background-color: linear-gradient(to bottom, #667eea, #764ba2); -fx-padding: 32; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4);">
        <HBox alignment="CENTER_LEFT" spacing="20">
            <VBox spacing="8" HBox.hgrow="ALWAYS">
                <Label style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700; -fx-font-family: 'Segoe UI';" text="Bienvenue sur LocationV1" />
                <Label style="-fx-text-fill: rgba(255,255,255,0.9); -fx-font-size: 16px; -fx-font-family: 'Segoe UI';" text="Gérez efficacement votre parc de véhicules et vos locations" />
            </VBox>
            <VBox alignment="CENTER_RIGHT" spacing="8">
                <Label fx:id="lblCurrentDate" style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 14px; -fx-font-weight: 500; -fx-font-family: 'Segoe UI';" text="Date: " />
                <Label style="-fx-text-fill: rgba(255,255,255,0.6); -fx-font-size: 12px; -fx-font-family: 'Segoe UI';" text="Dernière mise à jour: maintenant" />
            </VBox>
        </HBox>
    </VBox>
    <!-- Modern Statistics Cards -->
    <HBox alignment="CENTER" spacing="20">
        <!-- Total Clients Card -->
        <VBox spacing="16" style="-fx-background-color: white; -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 12, 0, 0, 4); -fx-min-width: 200;" styleClass="modern-card" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="linear-gradient(to bottom, #3b82f6, #1d4ed8)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(59,130,246,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="👥" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: #64748b; -fx-font-size: 13px; -fx-font-weight: 600;" styleClass="text-caption" text="TOTAL CLIENTS" />
                    <Label fx:id="lblTotalClients" style="-fx-text-fill: #1e293b; -fx-font-size: 32px; -fx-font-weight: 700;" styleClass="text-heading-2" text="0" />
                    <Label style="-fx-text-fill: #10b981; -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ +12% ce mois" />
                </VBox>
            </HBox>
        </VBox>
        <!-- Total Vehicles Card -->
        <VBox spacing="16" style="-fx-background-color: linear-gradient(to bottom, #10b981, #059669); -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(16,185,129,0.3), 12, 0, 0, 4); -fx-min-width: 200;" styleClass="modern-card" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="rgba(255,255,255,0.2)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(255,255,255,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="🚗" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 13px; -fx-font-weight: 600;" styleClass="text-caption" text="TOTAL VÉHICULES" />
                    <Label fx:id="lblTotalVehicules" style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700;" styleClass="text-heading-2" text="0" />
                    <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ Flotte complète" />
                </VBox>
            </HBox>
        </VBox>
        <!-- Active Rentals Card -->
        <VBox spacing="16" style="-fx-background-color: linear-gradient(to bottom, #f59e0b, #d97706); -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(245,158,11,0.3), 12, 0, 0, 4); -fx-min-width: 200;" styleClass="modern-card" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="rgba(255,255,255,0.2)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(255,255,255,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="📅" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 13px; -fx-font-weight: 600;" styleClass="text-caption" text="LOCATIONS ACTIVES" />
                    <Label fx:id="lblActiveLocations" style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700;" styleClass="text-heading-2" text="0" />
                    <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ En cours" />
                </VBox>
            </HBox>
        </VBox>
        <!-- Total Revenue Card -->
        <VBox spacing="16" style="-fx-background-color: linear-gradient(to bottom, #8b5cf6, #7c3aed); -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(139,92,246,0.3), 12, 0, 0, 4); -fx-min-width: 200;" styleClass="modern-card" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="rgba(255,255,255,0.2)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(255,255,255,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="💰" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 13px; -fx-font-weight: 600;" styleClass="text-caption" text="REVENUS TOTAUX" />
                    <Label fx:id="lblTotalRevenue" style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700;" styleClass="text-heading-2" text="0 DH" />
                    <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ +8% ce mois" />
                </VBox>
            </HBox>
        </VBox>
        <!-- Returns Today Card -->
        <VBox spacing="16" style="-fx-background-color: linear-gradient(to bottom, #ef4444, #dc2626); -fx-padding: 24; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(239,68,68,0.3), 12, 0, 0, 4); -fx-min-width: 200;" styleClass="modern-card" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="16">
                <StackPane>
                    <Circle fill="rgba(255,255,255,0.2)" radius="28" style="-fx-effect: dropshadow(gaussian, rgba(255,255,255,0.3), 8, 0, 0, 2);" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 20px;" text="⏰" />
                </StackPane>
                <VBox spacing="4" HBox.hgrow="ALWAYS">
                    <Label style="-fx-text-fill: rgba(255,255,255,0.8); -fx-font-size: 13px; -fx-font-weight: 600;" styleClass="text-caption" text="RETOURS AUJOURD'HUI" />
                    <Label fx:id="lblReturnsToday" style="-fx-text-fill: white; -fx-font-size: 32px; -fx-font-weight: 700;" styleClass="text-heading-2" text="0" />
                    <Label style="-fx-text-fill: rgba(255,255,255,0.7); -fx-font-size: 12px; -fx-font-weight: 500;" text="↗ Prévus aujourd'hui" />
                </VBox>
            </HBox>
        </VBox>
    </HBox>
    <!-- Charts Section -->
    <HBox spacing="24.0" style="-fx-padding: 0 0 0 0;">
        <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 32; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4);" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="12">
                <Label style="-fx-font-size: 20px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="📊 Statut des Véhicules" />
                <Region HBox.hgrow="ALWAYS" />
                <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: 500;" text="Répartition actuelle" />
            </HBox>
            <PieChart fx:id="vehiculeStatusPie" prefHeight="280.0" prefWidth="350.0" style="-fx-background-color: transparent;" />
        </VBox>
        <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 32; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4);" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="12">
                <Label style="-fx-font-size: 20px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="📈 Locations par Mois" />
                <Region HBox.hgrow="ALWAYS" />
                <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: 500;" text="12 derniers mois" />
            </HBox>
            <BarChart fx:id="locationsBarChart" prefHeight="280.0" prefWidth="450.0" style="-fx-background-color: transparent;">
                <xAxis>
                    <CategoryAxis fx:id="locationsMonthAxis" style="-fx-tick-label-fill: #64748b;" />
                </xAxis>
                <yAxis>
                    <NumberAxis fx:id="locationsCountAxis" style="-fx-tick-label-fill: #64748b;" />
                </yAxis>
            </BarChart>
        </VBox>
        <VBox spacing="16.0" style="-fx-background-color: white; -fx-padding: 32; -fx-background-radius: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 12, 0, 0, 4);" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="12">
                <Label style="-fx-font-size: 20px; -fx-font-weight: 700; -fx-text-fill: #1e293b; -fx-font-family: 'Segoe UI';" text="💰 Revenus par Mois" />
                <Region HBox.hgrow="ALWAYS" />
                <Label style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-weight: 500;" text="En DH" />
            </HBox>
            <BarChart fx:id="revenueBarChart" prefHeight="280.0" prefWidth="450.0" style="-fx-background-color: transparent;">
                <xAxis>
                    <CategoryAxis fx:id="revenueMonthAxis" style="-fx-tick-label-fill: #64748b;" />
                </xAxis>
                <yAxis>
                    <NumberAxis fx:id="revenueAmountAxis" style="-fx-tick-label-fill: #64748b;" />
                </yAxis>
            </BarChart>
        </VBox>
    </HBox>
    <HBox alignment="CENTER_LEFT" spacing="20.0">
        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Réservations (Locations)" />
        <Button fx:id="btnRefreshReservations" onAction="#handleRefreshReservations" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Actualiser" />
        <Button fx:id="btnAllLocations" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Voir tout" />
        <Button fx:id="btnExportLocations" style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Exporter" />
    </HBox>
    <TableView fx:id="reservationTable" prefHeight="370.0" prefWidth="1352.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-table-header-border-color: #e2e8f0; -fx-font-size: 13px;">
        <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
        </columnResizePolicy>
        <placeholder>
            <Label style="-fx-text-fill: #64748b; -fx-font-size: 15px;" text="Aucune donnée à afficher" />
        </placeholder>
        <columns>
            <TableColumn fx:id="idColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="ID" />
            <TableColumn fx:id="clientColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Client" />
            <TableColumn fx:id="vehiculeColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Véhicule" />
            <TableColumn fx:id="dateDebutColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Date Début" />
            <TableColumn fx:id="dateFinColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Date Fin" />
            <TableColumn fx:id="statutColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Statut" />
        </columns>
    </TableView>
    <HBox alignment="CENTER_LEFT" spacing="20.0">
        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40;" text="Historique des Paiements" />
        <Button fx:id="btnRefreshPayments" onAction="#handleRefreshPayments" style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Actualiser" />
        <Button fx:id="btnAllPayments" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Voir tout" />
    </HBox>
    <TableView fx:id="paymentTable" prefHeight="632.0" prefWidth="1352.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-table-header-border-color: #e2e8f0; -fx-font-size: 13px;">
        <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
        </columnResizePolicy>
        <placeholder>
            <Label style="-fx-text-fill: #64748b; -fx-font-size: 15px;" text="Aucune donnée à afficher" />
        </placeholder>
        <columns>
            <TableColumn fx:id="payIdColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="ID" />
            <TableColumn fx:id="payClientColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Client" />
            <TableColumn fx:id="payVehiculeColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Véhicule" />
            <TableColumn fx:id="payMontantColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Montant" />
            <TableColumn fx:id="payDateColumn" maxWidth="Infinity" style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;" text="Date Paiement" />
        </columns>
    </TableView>
    <!-- Recent Activity Section -->
    <VBox spacing="18.0" style="-fx-background-color: white; -fx-padding: 28; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);" VBox.vgrow="ALWAYS">
        <HBox alignment="CENTER_LEFT" spacing="20.0">
            <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';" text="Activité Récente" />
            <Button fx:id="btnAllActivity" style="-fx-background-color: #64748b; -fx-text-fill: white; -fx-background-radius: 8; -fx-padding: 8 16; -fx-font-size: 13px;" text="Voir tout" />
        </HBox>
        <TableView fx:id="recentActivityTable" prefHeight="758.0" prefWidth="1434.0" style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 10; -fx-table-header-border-color: #e2e8f0; -fx-font-size: 13px;" VBox.vgrow="ALWAYS">
            <columnResizePolicy>
                <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
            </columnResizePolicy>
            <placeholder>
                <Label style="-fx-text-fill: #64748b; -fx-font-size: 15px;" text="Aucune donnée à afficher" />
            </placeholder>
            <columns>
                <TableColumn fx:id="activityDateColumn" maxWidth="Infinity" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Date" />
                <TableColumn fx:id="activityTypeColumn" maxWidth="Infinity" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Type" />
                <TableColumn fx:id="activityDescriptionColumn" maxWidth="Infinity" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Description" />
                <TableColumn fx:id="activityUserColumn" maxWidth="Infinity" style="-fx-text-fill: #1e293b; -fx-font-weight: bold;" text="Utilisateur" />
            </columns>
        </TableView>
    </VBox>
        </VBox>
    </content>
</ScrollPane>
