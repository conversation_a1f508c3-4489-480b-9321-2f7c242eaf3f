<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.shape.*?>

<ScrollPane fitToWidth="true" hbarPolicy="NEVER" style="-fx-background: transparent; -fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.VehiculeController">
    <content>
        <VBox style="-fx-background-color: #f0f4f8;" spacing="20.0" prefHeight="1200">

    <padding>
        <Insets bottom="150.0" left="20.0" right="20.0" top="20.0"/>
    </padding>

    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="20.0"
          style="-fx-background-color: linear-gradient(to right, #1a3c40, #2d5f64); -fx-padding: 15 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 5);">

        <VBox spacing="5.0">
            <Label style="-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: white; -fx-font-family: 'Segoe UI';"
                   text="Gestion des Véhicules"/>
            <Label style="-fx-font-size: 13px; -fx-text-fill: rgba(255,255,255,0.8); -fx-font-family: 'Segoe UI';"
                   text="Gérez tous les véhicules de votre flotte"/>
        </VBox>

        <Region HBox.hgrow="ALWAYS"/>

        <HBox spacing="10.0">
            <Button fx:id="btnRefresh" onAction="#handleRefresh"
                    style="-fx-background-color: rgba(255,255,255,0.2); -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-family: 'Segoe UI'; -fx-cursor: hand;"
                    text="🔄 Actualiser"/>
            <Button fx:id="btnExport" onAction="#handleExport"
                    style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 12px; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-family: 'Segoe UI'; -fx-font-weight: bold; -fx-cursor: hand;"
                    text="📊 Exporter"/>
        </HBox>
    </HBox>

    <!-- Scrollable Main Content Area -->
    <ScrollPane fitToWidth="true" hbarPolicy="NEVER"
                style="-fx-background: transparent; -fx-background-color: transparent; -fx-padding: 0;">
        <HBox spacing="15.0">

            <!-- Left Panel - Vehicle List -->
            <VBox prefWidth="700.0" spacing="15.0"
                  style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.05), 5, 0, 0, 1);"
                  HBox.hgrow="ALWAYS">

                <!-- Search and Filter Section -->
                <VBox spacing="10.0">
                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';"
                           text="Recherche et Filtres"/>
                    <HBox spacing="8.0" alignment="CENTER_LEFT">
                        <TextField fx:id="searchField" promptText="Rechercher par marque, modèle, immatriculation..."
                                   style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px; -fx-pref-width: 400;"/>
                        <Button fx:id="btnSearch" onAction="#handleRechercher"
                                style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="🔍 Rechercher"/>
                        <Button fx:id="btnClearFilters" onAction="#handleClearFilters"
                                style="-fx-background-color: #94a3b8; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="Effacer"/>
                    </HBox>
                </VBox>

                <!-- Vehicle Table Section -->
                <VBox spacing="10.0">
                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI';"
                               text="Liste des Véhicules"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Label fx:id="lblTotalCount"
                               style="-fx-font-size: 12px; -fx-text-fill: #64748b; -fx-font-family: 'Segoe UI';"
                               text="Total: 0"/>
                    </HBox>

                    <TableView fx:id="vehiculeTable" prefHeight="400.0"
                               style="-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-radius: 6; -fx-table-header-border-color: #e2e8f0; -fx-font-size: 13px;"
                               VBox.vgrow="ALWAYS">
                        <columns>
                            <TableColumn fx:id="idColumn" prefWidth="50.0" text="ID"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold; -fx-alignment: CENTER;"/>
                            <TableColumn fx:id="marqueColumn" prefWidth="120.0" text="Marque"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;"/>
                            <TableColumn fx:id="modeleColumn" prefWidth="120.0" text="Modèle"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;"/>
                            <TableColumn fx:id="immatriculationColumn" prefWidth="120.0" text="Immatriculation"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold;"/>
                            <TableColumn fx:id="etatColumn" prefWidth="100.0" text="État"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold; -fx-alignment: CENTER;"/>
                            <TableColumn fx:id="prixParJourColumn" prefWidth="100.0" text="Prix/Jour"
                                         style="-fx-text-fill: #1a3c40; -fx-font-weight: bold; -fx-alignment: CENTER_RIGHT;"/>
                        </columns>
                    </TableView>

                    <!-- Action Buttons -->
                    <HBox alignment="CENTER_RIGHT" spacing="8.0">
                        <Button fx:id="btnAddVehicule" onAction="#handleAjouter"
                                style="-fx-background-color: #1a3c40; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="➕ Ajouter"/>
                        <Button fx:id="btnUpdateVehicule" onAction="#handleModifier"
                                style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="✏️ Modifier"/>
                        <Button fx:id="btnDeleteVehicule" onAction="#handleSupprimer"
                                style="-fx-background-color: #ef4444; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="🗑️ Supprimer"/>
                    </HBox>

                    <!-- State Change Section -->
                    <HBox spacing="8" alignment="CENTER_LEFT"
                          style="-fx-padding: 15 0 0 0; -fx-border-color: #e2e8f0; -fx-border-width: 1 0 0 0;">
                        <Label text="Changer l'état :"
                               style="-fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-size: 13px;"/>
                        <ComboBox fx:id="etatCombo" promptText="Sélectionner état"
                                  style="-fx-background-radius: 6; -fx-pref-width: 150; -fx-font-size: 13px;"/>
                        <Button fx:id="btnChangeState" text="Appliquer" onAction="#handleChangerEtat"
                                style="-fx-background-color: #6C8AE4; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"/>
                    </HBox>
                </VBox>
            </VBox>

            <!-- Right Panel - Vehicle Details/Form -->
            <VBox prefWidth="400.0" spacing="15.0">
                <VBox spacing="8.0"
                      style="-fx-background-color: white; -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.05), 5, 0, 0, 1);">

                    <Label style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-font-family: 'Segoe UI'; -fx-padding: 0 0 10 0;"
                           text="Détails du Véhicule"/>

                    <!-- Vehicle Form Fields -->
                    <GridPane hgap="8" vgap="8">
                        <columnConstraints>
                            <ColumnConstraints percentWidth="100"/>
                        </columnConstraints>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Marque *" GridPane.rowIndex="0"/>
                        <TextField fx:id="txtMarqueForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="1"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Modèle *" GridPane.rowIndex="2"/>
                        <TextField fx:id="txtModeleForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="3"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Immatriculation *" GridPane.rowIndex="4"/>
                        <TextField fx:id="txtImmatriculationForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="5"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="État *" GridPane.rowIndex="6"/>
                        <ComboBox fx:id="txtEtatForm" promptText="Sélectionner l'état" style="-fx-background-radius: 6; -fx-font-size: 13px;" GridPane.rowIndex="7"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Prix par Jour (DH) *" GridPane.rowIndex="8"/>
                        <TextField fx:id="txtPrixParJourForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="9"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Photo du véhicule" GridPane.rowIndex="10"/>
                        <HBox spacing="8.0" alignment="CENTER_LEFT" GridPane.rowIndex="11">
                            <ImageView fx:id="imgPreview" fitWidth="120" fitHeight="80" preserveRatio="true"
                                       style="-fx-border-color: #e2e8f0; -fx-border-radius: 6; -fx-background-color: #f8fafc;"/>
                            <Button fx:id="btnChooseImage" text="Choisir une image..." onAction="#handleChooseImage"
                                    style="-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"/>
                        </HBox>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Carburant *" GridPane.rowIndex="12"/>
                        <ComboBox fx:id="txtCarburantForm" promptText="Sélectionner le carburant" style="-fx-background-radius: 6; -fx-font-size: 13px;" GridPane.rowIndex="13"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Métrage (km) *" GridPane.rowIndex="14"/>
                        <TextField fx:id="txtMetrageForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="15"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Date d'acquisition *" GridPane.rowIndex="16"/>
                        <DatePicker fx:id="txtDateAcquisitionForm" style="-fx-background-radius: 6; -fx-font-size: 13px;" GridPane.rowIndex="17"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Dernière utilisation" GridPane.rowIndex="18"/>
                        <DatePicker fx:id="txtLastUsedForm" editable="false" disable="true" style="-fx-background-radius: 6; -fx-font-size: 13px; -fx-opacity: 0.7;" GridPane.rowIndex="19"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Nombre de chevaux" GridPane.rowIndex="20"/>
                        <TextField fx:id="txtNbreChevauxForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="21"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Assurance - Compagnie" GridPane.rowIndex="22"/>
                        <TextField fx:id="txtAssuranceCompagnieForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="23"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Assurance - Expiration" GridPane.rowIndex="24"/>
                        <DatePicker fx:id="txtAssuranceExpirationForm" style="-fx-background-radius: 6; -fx-font-size: 13px;" GridPane.rowIndex="25"/>

                        <Label style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #475569;" text="Assurance - Numéro" GridPane.rowIndex="26"/>
                        <TextField fx:id="txtAssuranceNumeroForm" style="-fx-background-radius: 6; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8; -fx-font-size: 13px;" GridPane.rowIndex="27"/>
                    </GridPane>

                    <!-- Form Action Buttons -->
                    <HBox alignment="CENTER_RIGHT" spacing="8.0" style="-fx-padding: 15 0 0 0;">
                        <Button fx:id="btnCancel" onAction="#handleCancel"
                                style="-fx-background-color: transparent; -fx-text-fill: #64748b; -fx-border-color: #d1d5db; -fx-border-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="Annuler"/>
                        <Button fx:id="btnSave" onAction="#handleSave"
                                style="-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 15; -fx-font-size: 13px; -fx-cursor: hand;"
                                text="💾 Enregistrer"/>
                    </HBox>
                </VBox>
            </VBox>
        </HBox>
    </ScrollPane>
        </VBox>
    </content>
</ScrollPane>