import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import util.WindowUtil;

public class MainApp extends Application {
    @Override
    public void start(Stage primaryStage) throws Exception {
        Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
        primaryStage.setTitle("Location de voitures - Admin");
        primaryStage.setScene(new Scene(root, 500, 800));

        // Use proper window sizing to avoid taskbar overlap
        WindowUtil.maximizeWindowConservative(primaryStage);
        primaryStage.show();
    }



    public static void showClientLogin() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(MainApp.class.getResource("/view/client_login.fxml"));
            javafx.scene.Parent root = loader.load();
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Connexion Client");
            stage.setScene(new javafx.scene.Scene(root, 500, 800));

            // Use proper window sizing to avoid taskbar overlap
            WindowUtil.maximizeWindowConservative(stage);

            stage.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        util.DatabaseInitializer.initialize();
        launch(args);
    }
}
