package controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import model.Location;
import model.Client;
import model.User;
import model.Vehicule;
import javafx.stage.Stage;
import util.WindowUtil;

public class LocationDetailController {
    @FXML private Label lblClient;
    @FXML private Label lblVehicule;
    @FXML private Label lblLocationDates;
    @FXML private Label lblStatus;
    @FXML private Label lblPrixTotal;
    @FXML private Label lblPenalite;
    @FXML private Label lblCreatedBy;
    @FXML private ImageView imgVehicule;
    @FXML private Label lblVehiculeMarque;
    @FXML private Label lblVehiculeModele;
    @FXML private Label lblVehiculeImmat;
    @FXML private Label lblVehiculeEtat;
    @FXML private Label lblVehiculeCarburant;
    @FXML private Label lblVehiculePrixJour;
    @FXML private Label lblVehiculeMetrage;
    @FXML private Label lblClientNom;
    @FXML private Label lblClientPrenom;
    @FXML private Label lblClientCIN;
    @FXML private Label lblClientTel;
    @FXML private Label lblClientEmail;
    @FXML private Label lblLocationDebut;
    @FXML private Label lblLocationFinPrevue;
    @FXML private Label lblLocationFinReelle;
    @FXML private VBox root;

    @FXML
    private void handleModify() {
        // Only allow for admin (pseudo-check, replace with real role check)
        if (!isAdmin()) {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.WARNING);
            alert.setTitle("Accès refusé");
            alert.setHeaderText(null);
            alert.setContentText("Seul un administrateur peut modifier cette location.");
            alert.showAndWait();
            return;
        }
        // Pass the location to the create/edit controller (static context or service)
        controller.LocationCreateController.setLocationToEdit(currentLocation);
        // Close this window
        closeWindow();
        // Open the location create/edit window
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            javafx.scene.Parent root = loader.load();
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Modifier la Location");
            stage.setScene(new javafx.scene.Scene(root));
            stage.setMaximized(true);
            // Pre-fill data in controller
            controller.LocationCreateController ctrl = loader.getController();
            ctrl.prefillForm(currentLocation);
            stage.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isAdmin() {
        // Check if logged in user is admin
        if (LoginController.loggedInUser != null) {
            try {
                User user = (User) LoginController.loggedInUser;
                return "admin".equals(user.getRole());
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    private Location currentLocation;
    public void setLocation(Location location) {
        this.currentLocation = location;
        if (location == null) return;
        Client client = location.getClient();
        Vehicule vehicule = location.getVehicule();
        // Car details
        if (vehicule != null) {
            lblVehiculeMarque.setText(vehicule.getMarque());
            lblVehiculeModele.setText(vehicule.getModele());
            lblVehiculeImmat.setText(vehicule.getImmatriculation());
            lblVehiculeEtat.setText(vehicule.getEtat());
            lblVehiculeCarburant.setText(vehicule.getCarburant());
            lblVehiculePrixJour.setText(vehicule.getPrixParJour() + " DH");
            lblVehiculeMetrage.setText(vehicule.getMetrage() != null ? vehicule.getMetrage().toString() : "");
            if (vehicule.getPhotoUrl() != null && !vehicule.getPhotoUrl().isEmpty()) {
                try {
                    imgVehicule.setImage(new Image(vehicule.getPhotoUrl(), 180, 120, true, true));
                } catch (Exception e) { imgVehicule.setImage(null); }
            } else {
                imgVehicule.setImage(null);
            }
        }
        // Client details
        if (client != null) {
            lblClientNom.setText(client.getNom());
            lblClientPrenom.setText(client.getPrenom());
            lblClientCIN.setText(client.getCin());
            lblClientTel.setText(client.getTelephone());
            lblClientEmail.setText(client.getEmail());
        }
        // Location details
        lblLocationDebut.setText(location.getDateDebut() != null ? location.getDateDebut().toString() : "");
        lblLocationFinPrevue.setText(location.getDateFinPrevue() != null ? location.getDateFinPrevue().toString() : "");
        lblLocationFinReelle.setText(location.getDateFinReelle() != null ? location.getDateFinReelle().toString() : "");
        lblStatus.setText(location.getStatus() != null ? location.getStatus().name() : "");
        lblPrixTotal.setText(location.getPrixTotal() + " DH");
        lblPenalite.setText(location.getPenalite() + " DH");
        lblCreatedBy.setText(""); // If you have a createdBy/agent field, show it; else leave blank
    }

    public void closeWindow() {
        javafx.stage.Stage stage = (javafx.stage.Stage) root.getScene().getWindow();
        stage.close();
    }
} 