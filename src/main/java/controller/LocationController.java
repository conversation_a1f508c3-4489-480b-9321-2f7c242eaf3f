package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import dao.LocationDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import model.Location;
import model.Client;
import model.Vehicule;
import java.util.List;
import java.util.stream.Collectors;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import java.time.LocalDate;

public class LocationController {
    @FXML
    private TableView<Location> locationTable;
    @FXML
    private TableColumn<Location, Long> idColumn;
    @FXML
    private TableColumn<Location, String> clientColumn;
    @FXML
    private TableColumn<Location, String> vehiculeColumn;
    @FXML
    private TableColumn<Location, String> dateDebutColumn;
    @FXML
    private TableColumn<Location, String> dateFinColumn;
    @FXML
    private TableColumn<Location, String> statutColumn;
    @FXML
    private TextField searchField;
    @FXML
    private Label lblTotalCount;
    
    // Form elements
    @FXML private ComboBox<Client> txtClientForm;
    @FXML private ComboBox<Vehicule> txtVehiculeForm;
    @FXML private DatePicker txtDateDebutForm;
    @FXML private DatePicker txtDateFinForm;
    @FXML private ComboBox<String> txtStatutForm;
    @FXML private Button btnSave;
    @FXML private Button btnCancel;

    private final LocationDAO locationDAO = new LocationDAO();
    private final ClientDAO clientDAO = new ClientDAO();
    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();
    private ObservableList<Location> locationList;
    private Location selectedLocation = null;
    private boolean isEditMode = false;

    @FXML
    public void initialize() {
        idColumn.setCellValueFactory(data -> new javafx.beans.property.SimpleLongProperty(data.getValue().getId()).asObject());
        clientColumn.setCellValueFactory(data -> {
            model.Client c = data.getValue().getClient();
            String name = c != null ? c.getNom() + " " + c.getPrenom() : "";
            return new javafx.beans.property.SimpleStringProperty(name);
        });
        vehiculeColumn.setCellValueFactory(data -> {
            model.Vehicule v = data.getValue().getVehicule();
            String veh = v != null ? v.getMarque() + " " + v.getModele() : "";
            return new javafx.beans.property.SimpleStringProperty(veh);
        });
        dateDebutColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateDebut();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        dateFinColumn.setCellValueFactory(data -> {
            java.time.LocalDate d = data.getValue().getDateFinPrevue();
            return new javafx.beans.property.SimpleStringProperty(d != null ? d.toString() : "");
        });
        statutColumn.setCellValueFactory(data -> {
            model.Location l = data.getValue();
            String statut = l.getStatus() != null ? l.getStatus().name() : (l.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
            return new javafx.beans.property.SimpleStringProperty(statut);
        });
        
        // Setup combo boxes
        ObservableList<String> statuts = FXCollections.observableArrayList("En cours", "Terminée", "Annulée");
        txtStatutForm.setItems(statuts);
        
        // Add table selection listener
        locationTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                selectedLocation = newSelection;
                loadLocationToForm(newSelection);
                isEditMode = true;
                btnSave.setText("Modifier");
            } else {
                clearForm();
            }
        });
        
        loadLocations();
        loadComboBoxes();
        clearForm();
    }

    private void loadLocations() {
        List<Location> list = locationDAO.findAll();
        locationList = FXCollections.observableArrayList(list);
        locationTable.setItems(locationList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + list.size());
        }
    }

    private void loadComboBoxes() {
        // Load clients
        List<Client> clients = clientDAO.findAll();
        txtClientForm.setItems(FXCollections.observableArrayList(clients));
        txtClientForm.setCellFactory(param -> new ListCell<Client>() {
            @Override
            protected void updateItem(Client item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getNom() + " " + item.getPrenom() + (item.getEmail() != null && !item.getEmail().isBlank() ? " (" + item.getEmail() + ")" : ""));
                }
            }
        });
        txtClientForm.setButtonCell(txtClientForm.getCellFactory().call(null));
        txtClientForm.setConverter(new javafx.util.StringConverter<Client>() {
            @Override
            public String toString(Client c) {
                if (c == null) return "";
                return c.getNom() + " " + c.getPrenom() + (c.getEmail() != null && !c.getEmail().isBlank() ? " (" + c.getEmail() + ")" : "");
            }
            @Override
            public Client fromString(String s) { return null; }
        });

        // Load vehicles
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        txtVehiculeForm.setItems(FXCollections.observableArrayList(vehicules));
        txtVehiculeForm.setCellFactory(param -> new ListCell<Vehicule>() {
            @Override
            protected void updateItem(Vehicule item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.getMarque() + " " + item.getModele() + (item.getImmatriculation() != null ? " (" + item.getImmatriculation() + ")" : ""));
                }
            }
        });
        txtVehiculeForm.setButtonCell(txtVehiculeForm.getCellFactory().call(null));
        txtVehiculeForm.setConverter(new javafx.util.StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule v) {
                if (v == null) return "";
                return v.getMarque() + " " + v.getModele() + (v.getImmatriculation() != null ? " (" + v.getImmatriculation() + ")" : "");
            }
            @Override
            public Vehicule fromString(String s) { return null; }
        });
    }

    private void loadLocationToForm(Location location) {
        txtClientForm.setValue(location.getClient());
        txtVehiculeForm.setValue(location.getVehicule());
        txtDateDebutForm.setValue(location.getDateDebut());
        txtDateFinForm.setValue(location.getDateFinPrevue());
        String statut = location.getStatus() != null ? location.getStatus().name() : (location.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
        txtStatutForm.setValue(statut);
    }

    private void clearForm() {
        txtClientForm.setValue(null);
        txtVehiculeForm.setValue(null);
        txtDateDebutForm.setValue(null);
        txtDateFinForm.setValue(null);
        txtStatutForm.setValue(null);
        selectedLocation = null;
        isEditMode = false;
        btnSave.setText("Enregistrer");
    }

    @FXML
    private void handleAjouter() {
        clearForm();
    }

    @FXML
    private void handleModifier() {
        // No longer needed, but keep for compatibility
        if (selectedLocation != null) {
            isEditMode = true;
            btnSave.setText("Modifier");
        } else {
            showAlert("Veuillez sélectionner une location à modifier", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSupprimer() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Supprimer la location");
            alert.setContentText("Êtes-vous sûr de vouloir supprimer cette location ?");
            
            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        locationDAO.delete(selected);
                        loadLocations();
                        clearForm();
                        showAlert("Location supprimée avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de la suppression: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner une location à supprimer", AlertType.WARNING);
        }
    }

    @FXML
    private void handleSave() {
        Client client = txtClientForm.getValue();
        Vehicule vehicule = txtVehiculeForm.getValue();
        LocalDate dateDebut = txtDateDebutForm.getValue();
        LocalDate dateFin = txtDateFinForm.getValue();
        String statut = txtStatutForm.getValue();

        if (client == null || vehicule == null || dateDebut == null || dateFin == null) {
            showAlert("Veuillez remplir tous les champs obligatoires", AlertType.WARNING);
            return;
        }

        if (dateFin.isBefore(dateDebut)) {
            showAlert("La date de fin doit être postérieure à la date de début", AlertType.WARNING);
            return;
        }

        Long excludeId = isEditMode && selectedLocation != null ? selectedLocation.getId() : null;
        boolean available = locationDAO.isVehiculeAvailable(
            vehicule.getId(), dateDebut, dateFin, excludeId
        );
        if (!available) {
            showAlert("Ce véhicule est déjà réservé pour cette période.", AlertType.ERROR);
            return;
        }

        try {
            if (isEditMode && selectedLocation != null) {
                // Update existing location
                selectedLocation.setClient(client);
                selectedLocation.setVehicule(vehicule);
                selectedLocation.setDateDebut(dateDebut);
                selectedLocation.setDateFinPrevue(dateFin);
                if ("Terminée".equals(statut)) {
                    selectedLocation.setDateFinReelle(LocalDate.now());
                }
                selectedLocation.updateStatus();
                locationDAO.save(selectedLocation);
                showAlert("Location modifiée avec succès", AlertType.INFORMATION);
            } else {
                // Create new location
                Location newLocation = new Location();
                newLocation.setClient(client);
                newLocation.setVehicule(vehicule);
                newLocation.setDateDebut(dateDebut);
                newLocation.setDateFinPrevue(dateFin);
                newLocation.updateStatus();
                locationDAO.save(newLocation);
                showAlert("Location ajoutée avec succès", AlertType.INFORMATION);
            }
            loadLocations();
            clearForm();
        } catch (Exception e) {
            showAlert("Erreur lors de l'enregistrement: " + e.getMessage(), AlertType.ERROR);
        }
    }

    @FXML
    private void handleCancel() {
        clearForm();
    }

    @FXML
    private void handleRefresh() {
        searchField.clear();
        loadLocations();
        clearForm();
    }

    @FXML
    private void handleExport() {
        Alert alert = new Alert(AlertType.INFORMATION);
        alert.setTitle("Export");
        alert.setHeaderText(null);
        alert.setContentText("Fonction d'export non implémentée (stub).");
        alert.showAndWait();
    }

    @FXML
    private void handleRechercher() {
        String keyword = searchField.getText().toLowerCase();
        if (keyword.isEmpty()) {
            loadLocations();
            return;
        }
        List<Location> filtered = locationDAO.findAll().stream()
                .filter(l -> {
                    String client = l.getClient() != null ? l.getClient().getNom() + " " + l.getClient().getPrenom() : "";
                    String vehicule = l.getVehicule() != null ? l.getVehicule().getMarque() + " " + l.getVehicule().getModele() : "";
                    String statut = l.getStatus() != null ? l.getStatus().name() : (l.getDateFinReelle() == null ? "EN_COURS" : "TERMINE");
                    return client.toLowerCase().contains(keyword)
                        || vehicule.toLowerCase().contains(keyword)
                        || statut.toLowerCase().contains(keyword);
                })
                .collect(Collectors.toList());
        locationList.setAll(filtered);
        locationTable.setItems(locationList);
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + filtered.size());
        }
    }

    @FXML
    private void handleClearFilters() {
        searchField.clear();
        loadLocations();
    }

    @FXML
    private void handleAnnulerLocation() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (selected.getDateFinReelle() != null || (selected.getStatus() != null && selected.getStatus() == model.Location.Status.TERMINE)) {
                showAlert("Impossible d'annuler une location déjà terminée.", AlertType.WARNING);
                return;
            }
            Alert alert = new Alert(AlertType.CONFIRMATION);
            alert.setTitle("Confirmation");
            alert.setHeaderText("Annuler la location");
            alert.setContentText("Êtes-vous sûr de vouloir annuler cette location ?");
            alert.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        selected.setStatus(model.Location.Status.ANNULE);
                        selected.setDateFinReelle(java.time.LocalDate.now());
                        Vehicule v = selected.getVehicule();
                        if (v != null) {
                            v.setEtat("Disponible");
                            new dao.VehiculeDAO().save(v);
                        }
                        locationDAO.save(selected);
                        loadLocations();
                        showAlert("Location annulée avec succès", AlertType.INFORMATION);
                    } catch (Exception e) {
                        showAlert("Erreur lors de l'annulation: " + e.getMessage(), AlertType.ERROR);
                    }
                }
            });
        } else {
            showAlert("Veuillez sélectionner une location à annuler", AlertType.WARNING);
        }
    }

    // Extension logic for admin/agent
    @FXML
    private void handleProlongerLocation() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une location à prolonger", AlertType.WARNING);
            return;
        }
        if (selected.getStatus() == model.Location.Status.ANNULE || selected.getStatus() == model.Location.Status.TERMINE) {
            showAlert("Impossible de prolonger une location annulée ou terminée.", AlertType.WARNING);
            return;
        }
        TextInputDialog dialog = new TextInputDialog(selected.getDateFinPrevue() != null ? selected.getDateFinPrevue().toString() : "");
        dialog.setTitle("Prolonger la location");
        dialog.setHeaderText("Nouvelle date de fin prévue");
        dialog.setContentText("Entrez la nouvelle date de fin (AAAA-MM-JJ):");
        dialog.showAndWait().ifPresent(input -> {
            try {
                java.time.LocalDate newDateFin = java.time.LocalDate.parse(input);
                if (newDateFin.isBefore(selected.getDateDebut())) {
                    showAlert("La nouvelle date de fin doit être postérieure à la date de début.", AlertType.ERROR);
                    return;
                }
                boolean available = locationDAO.isVehiculeAvailable(
                    selected.getVehicule().getId(),
                    selected.getDateDebut(),
                    newDateFin,
                    selected.getId()
                );
                if (!available) {
                    showAlert("Ce véhicule est déjà réservé pour cette période.", AlertType.ERROR);
                    return;
                }
                selected.setDateFinPrevue(newDateFin);
                selected.updateStatus();
                locationDAO.save(selected);
                loadLocations();
                showAlert("Location prolongée avec succès", AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Date invalide ou erreur: " + e.getMessage(), AlertType.ERROR);
            }
        });
    }

    @FXML
    private void handleViewDetails() {
        Location selected = locationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une location à visualiser", AlertType.WARNING);
            return;
        }
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location_detail.fxml"));
            javafx.scene.Parent detailRoot = loader.load();
            controller.LocationDetailController detailController = loader.getController();
            detailController.setLocation(selected);
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Détails de la Location");
            stage.setScene(new javafx.scene.Scene(detailRoot));
            stage.setMaximized(true);
            stage.show();
        } catch (Exception e) {
            showAlert("Erreur lors de l'ouverture des détails: " + e.getMessage(), AlertType.ERROR);
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("LocationV1");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Extension logic stub (to be implemented in UI)
    // public void handleProlongerLocation() { ... }
}
