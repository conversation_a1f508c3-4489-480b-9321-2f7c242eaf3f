package controller;

import javafx.fxml.FXML;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import model.Vehicule;
import controller.LoginController;
import model.User;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Modality;
import javafx.stage.Stage;
import model.Admin;
import model.Agent;

public class VehiculeDetailController {
    @FXML
    private ImageView vehiculeImage;
    @FXML
    private Label marqueLabel;
    @FXML
    private Label modeleLabel;
    @FXML
    private Label immatriculationLabel;
    @FXML
    private Label etatLabel;
    @FXML
    private Label prixLabel;
    @FXML
    private Button btnLouer;
    @FXML
    private Button btnReserver;
    @FXML
    private Button btnHistorique;
    @FXML
    private Button btnDisponibilite;

    private Vehicule vehicule;

    public void setVehicule(Vehicule v) {
        this.vehicule = v;
        if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
            vehiculeImage.setImage(new Image(v.getPhotoUrl(), 320, 180, true, true));
        }
        marqueLabel.setText(v.getMarque());
        modeleLabel.setText(v.getModele());
        immatriculationLabel.setText("Immatriculation: " + v.getImmatriculation());
        etatLabel.setText("État: " + v.getEtat());
        prixLabel.setText(String.format("%.2f DH/jour", v.getPrixParJour()));
        Object user = LoginController.loggedInUser;
        btnLouer.setVisible(user instanceof Admin || user instanceof Agent);
    }

    @FXML
    private void handleLouer() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/location_create.fxml"));
            Parent root = loader.load();
            LocationCreateController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Nouvelle Location");
            dialog.setMaximized(true);
            dialog.showAndWait();
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
            alert.setTitle("Succès");
            alert.setHeaderText(null);
            alert.setContentText("Location créée avec succès !");
            alert.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleReserver() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/reserver_louer.fxml"));
            Parent root = loader.load();
            ReserverLouerController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Réserver / Louer");
            dialog.setMaximized(true);
            dialog.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleHistorique() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/historique.fxml"));
            Parent root = loader.load();
            HistoriqueController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Historique");
            dialog.setMaximized(true);
            dialog.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleDisponibilite() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/disponibilite.fxml"));
            Parent root = loader.load();
            DisponibiliteController ctrl = loader.getController();
            ctrl.setVehicule(vehicule);
            Stage dialog = new Stage();
            dialog.initModality(Modality.APPLICATION_MODAL);
            dialog.setScene(new Scene(root));
            dialog.setTitle("Disponibilité");
            dialog.setMaximized(true);
            dialog.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 