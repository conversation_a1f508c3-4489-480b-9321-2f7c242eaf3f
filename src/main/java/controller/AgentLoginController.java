package controller;

import dao.AgentDAO;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.stage.Stage;
import model.Agent;
import org.mindrot.jbcrypt.BCrypt;
import javafx.scene.Scene;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;

public class AgentLoginController {
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private Label errorLabel;

    private final AgentDAO agentDAO = new AgentDAO();

    @FXML
    private void handleLogin() {
        String username = usernameField.getText();
        String password = passwordField.getText();
        if (username.isEmpty() || password.isEmpty()) {
            errorLabel.setText("Veuillez remplir tous les champs.");
            return;
        }
        Agent agent = agentDAO.findByUsername(username);
        if (agent != null && BCrypt.checkpw(password, agent.getPasswordHash())) {
            // Login successful, route to main dashboard with agent role
            try {
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/dashboard.fxml"));
                Parent root = loader.load();
                DashboardController dashboardController = loader.getController();
                dashboardController.setUserRole(agent.getRole());
                Stage stage = (Stage) usernameField.getScene().getWindow();
                stage.setScene(new Scene(root, 1400, 900));
                WindowUtil.maximizeWindowConservative(stage);
                stage.setTitle("LocationV1 - Espace Agent");
            } catch (Exception e) {
                errorLabel.setText("Erreur lors du chargement du tableau de bord.");
            }
        } else {
            errorLabel.setText("Nom d'utilisateur ou mot de passe incorrect.");
        }
    }

    @FXML
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            handleLogin();
        }
    }

    @FXML
    private void handleBackToMain() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/login.fxml"));
            Parent root = loader.load();
            Stage stage = (Stage) usernameField.getScene().getWindow();
            stage.setScene(new Scene(root, 500, 800));
            WindowUtil.maximizeWindowConservative(stage);
            stage.setTitle("LocationV1 - Connexion");
        } catch (Exception e) {
            errorLabel.setText("Erreur lors du retour à l'accueil.");
        }
    }
}