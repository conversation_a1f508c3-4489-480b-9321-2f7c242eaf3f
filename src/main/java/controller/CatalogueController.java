package controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.Alert;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.TilePane;
import javafx.scene.layout.VBox;
import javafx.scene.control.Label;
import javafx.stage.Stage;
import util.WindowUtil;
import model.Vehicule;
import dao.VehiculeDAO;
import java.util.List;
import javafx.event.ActionEvent;
import javafx.collections.FXCollections;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TextField;
import javafx.scene.layout.HBox;
import javafx.scene.control.Dialog;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Button;
import javafx.scene.layout.Region;
import javafx.scene.Scene;
import controller.ReserverLouerController;
import controller.HistoriqueController;
import controller.DisponibiliteController;
import javafx.scene.layout.FlowPane;
import javafx.scene.control.TableView;
import javafx.scene.control.TableColumn;

public class CatalogueController {
    @FXML
    private FlowPane cataloguePane;
    @FXML
    private ComboBox<String> marqueFilter;
    @FXML
    private ComboBox<String> etatFilter;
    @FXML
    private TextField prixFilter;
    @FXML
    private TextField searchField;
    @FXML
    private Label lblTotalCount;

    private final VehiculeDAO vehiculeDAO = new VehiculeDAO();

    @FXML
    public void initialize() {
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        if (marqueFilter != null)
            marqueFilter.setItems(FXCollections.observableArrayList(
                vehicules.stream().map(Vehicule::getMarque).distinct().toList()
            ));
        if (etatFilter != null)
            etatFilter.setItems(FXCollections.observableArrayList(
                vehicules.stream().map(Vehicule::getEtat).distinct().toList()
            ));
        loadCatalogue();
    }

    private void loadCatalogue() {
        List<Vehicule> vehicules = vehiculeDAO.findAll();
        cataloguePane.getChildren().clear();
        for (Vehicule v : vehicules) {
            // Build card dynamically
            HBox card = new HBox(12);
            card.setStyle("-fx-background-color: #fff; -fx-border-color: #e2e8f0; -fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 12;");
            card.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
            // Image
            ImageView carImage = new ImageView();
            carImage.setFitHeight(80);
            carImage.setFitWidth(120);
            carImage.setStyle("-fx-background-radius: 8;");
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                try {
                    carImage.setImage(new Image(v.getPhotoUrl(), 120, 80, true, true));
                } catch (Exception e) { /* ignore */ }
            }
            // Details
            VBox details = new VBox(4);
            Label carTitle = new Label(v.getMarque() + " " + v.getModele());
            carTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
            Label carImmat = new Label("Matricule: " + (v.getImmatriculation() != null ? v.getImmatriculation() : ""));
            carImmat.setStyle("-fx-font-size: 12px; -fx-text-fill: #64748b;");
            HBox statusPrice = new HBox(6);
            String etatColor = "#10b981";
            if (v.getEtat() != null && v.getEtat().toLowerCase().contains("lou")) etatColor = "#f59e42";
            if (v.getEtat() != null && v.getEtat().toLowerCase().contains("panne")) etatColor = "#ef4444";
            Label carStatus = new Label(v.getEtat());
            carStatus.setStyle("-fx-background-color: " + etatColor + "; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 2 8;");
            Label carPrice = new Label(String.format("%.2f DH/jour", v.getPrixParJour()));
            carPrice.setStyle("-fx-font-size: 13px; -fx-font-weight: bold; -fx-text-fill: #3b82f6;");
            statusPrice.getChildren().addAll(carStatus, carPrice);
            HBox availRow = new HBox(4);
            Label carAvailability = new Label("Lun. Mar. Mer. Jeu. Ven. Sam. Dim.");
            carAvailability.setStyle("-fx-font-size: 11px; -fx-text-fill: #f59e42;");
            Label carReturnDate = new Label("Retour: -");
            carReturnDate.setStyle("-fx-font-size: 11px; -fx-text-fill: #64748b;");
            availRow.getChildren().addAll(carAvailability, carReturnDate);
            HBox actions = new HBox(8);
            Button btnLouer = new Button("Louer");
            btnLouer.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6;");
            btnLouer.setOnAction(e -> openLocationCreateWithVehicle(v));
            Button btnDetails = new Button("Voir détails");
            btnDetails.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6;");
            btnDetails.setOnAction(e -> showVehiculeDetail(v));
            actions.getChildren().addAll(btnLouer, btnDetails);
            details.getChildren().addAll(carTitle, carImmat, statusPrice, availRow, actions);
            card.getChildren().addAll(carImage, details);
            cataloguePane.getChildren().add(card);
        }
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + vehicules.size() + " véhicules");
        }
    }

    private HBox createVehicleCard(Vehicule v) {
        HBox card = new HBox(20);
        card.setStyle("-fx-background-color: #fff; -fx-background-radius: 12; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2); -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-border-width: 1; -fx-cursor: hand;");
        card.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        card.setOnMouseClicked(e -> showVehiculeDetail(v));

        // Vehicle image
        ImageView img = new ImageView();
        try {
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                img.setImage(new Image(v.getPhotoUrl(), 120, 80, true, true));
            }
        } catch (Exception e) { /* ignore */ }
        img.setFitWidth(120);
        img.setFitHeight(80);
        img.setStyle("-fx-background-color: #e2e8f0; -fx-background-radius: 8;");

        // Vehicle details
        VBox details = new VBox(8);
        details.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        
        Label marque = new Label(v.getMarque() + " " + v.getModele());
        marque.setStyle("-fx-font-weight: bold; -fx-font-size: 18px; -fx-text-fill: #1a3c40;");
        
        Label immatriculation = new Label("Immatriculation: " + (v.getImmatriculation() != null ? v.getImmatriculation() : ""));
        immatriculation.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        
        Label carburant = new Label("Carburant: " + (v.getCarburant() != null ? v.getCarburant() : ""));
        carburant.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
        
        details.getChildren().addAll(marque, immatriculation, carburant);

        // Price and status section
        VBox priceStatus = new VBox(8);
        priceStatus.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);
        
        Label prix = new Label(String.format("%.2f DH/jour", v.getPrixParJour()));
        prix.setStyle("-fx-text-fill: #3b82f6; -fx-font-size: 16px; -fx-font-weight: bold;");
        
        String etatColor = "#10b981";
        if (v.getEtat() != null && v.getEtat().toLowerCase().contains("lou")) etatColor = "#f59e42";
        if (v.getEtat() != null && v.getEtat().toLowerCase().contains("panne")) etatColor = "#ef4444";
        Label etat = new Label(v.getEtat());
        etat.setStyle("-fx-font-size: 13px; -fx-font-weight: bold; -fx-text-fill: " + etatColor + "; -fx-background-color: rgba(16,185,129,0.08); -fx-background-radius: 6; -fx-padding: 4 8 4 8; -fx-alignment: center;");
        
        priceStatus.getChildren().addAll(prix, etat);

        // Action buttons
        VBox actions = new VBox(8);
        actions.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);
        
        Button btnDetails = new Button("Voir détails");
        btnDetails.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16; -fx-font-size: 13px; -fx-cursor: hand;");
        btnDetails.setOnAction(e -> showVehiculeDetail(v));
        
        Button btnLouer = new Button("Louer");
        btnLouer.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-background-radius: 6; -fx-padding: 8 16; -fx-font-size: 13px; -fx-cursor: hand;");
        btnLouer.setOnAction(e -> openLocationCreateWithVehicle(v));
        
        actions.getChildren().addAll(btnDetails, btnLouer);

        // Add all sections to the card
        card.getChildren().addAll(img, details, new Region(), priceStatus, actions);
        HBox.setHgrow(details, javafx.scene.layout.Priority.ALWAYS);
        
        return card;
    }

    private void showVehiculeDetail(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Détail du Véhicule");
        dialog.getDialogPane().setStyle("-fx-background-color: #f8fafc;");
        VBox content = new VBox(20);
        content.setStyle("-fx-padding: 32; -fx-background-color: #fff; -fx-background-radius: 16; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.10), 10, 0, 0, 2);");
        Label header = new Label(v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 26px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-padding: 0 0 8 0;");
        ImageView img = new ImageView();
        try {
            if (v.getPhotoUrl() != null && !v.getPhotoUrl().isEmpty()) {
                img.setImage(new Image(v.getPhotoUrl(), 360, 200, true, true));
            }
        } catch (Exception e) { /* ignore */ }
        img.setFitWidth(360);
        img.setFitHeight(200);
        img.setStyle("-fx-background-color: #e2e8f0; -fx-background-radius: 10; margin-bottom: 16px;");
        VBox details = new VBox(8);
        details.getChildren().addAll(
            new Label("Immatriculation: " + v.getImmatriculation()),
            new Label(String.format("Prix: %.2f DH/jour", v.getPrixParJour())),
            new Label("État: " + v.getEtat()),
            new Label("Carburant: " + (v.getCarburant() != null ? v.getCarburant() : "")),
            new Label("Métrage: " + (v.getMetrage() != null ? v.getMetrage() + " km" : "")),
            new Label("Date d'acquisition: " + (v.getDateAcquisition() != null ? v.getDateAcquisition().toString() : "")),
            new Label("Dernière utilisation: " + (v.getLastUsed() != null ? v.getLastUsed().toString() : "")),
            new Label("Nombre de chevaux: " + (v.getNbreChevaux() != null ? v.getNbreChevaux() : "")),
            new Label("Assurance: " + (v.getAssuranceCompagnie() != null ? v.getAssuranceCompagnie() : "") + (v.getAssuranceExpiration() != null ? ", exp: " + v.getAssuranceExpiration() : "") + (v.getAssuranceNumero() != null ? ", n°: " + v.getAssuranceNumero() : ""))
        );
        details.setStyle("-fx-font-size: 15px; -fx-text-fill: #334155; -fx-padding: 0 0 8 0;");
        HBox actions = new HBox(18);
        actions.setStyle("-fx-padding: 24 0 0 0;");
        Button btnReserver = new Button("Réserver / Louer");
        btnReserver.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-font-size: 15px; -fx-background-radius: 10; -fx-padding: 10 28;");
        btnReserver.setOnAction(e -> { dialog.close(); showReservationDialog(v); });
        Button btnHistorique = new Button("Voir historique");
        btnHistorique.setStyle("-fx-background-color: #64748b; -fx-text-fill: white; -fx-font-size: 15px; -fx-background-radius: 10; -fx-padding: 10 28;");
        btnHistorique.setOnAction(e -> { dialog.close(); showHistoriqueDialog(v); });
        Button btnDisponibilite = new Button("Voir disponibilité");
        btnDisponibilite.setStyle("-fx-background-color: #10b981; -fx-text-fill: white; -fx-font-size: 15px; -fx-background-radius: 10; -fx-padding: 10 28;");
        btnDisponibilite.setOnAction(e -> { dialog.close(); showDisponibiliteDialog(v); });
        actions.getChildren().addAll(btnReserver, btnHistorique, btnDisponibilite);
        content.getChildren().addAll(header, img, details, actions);
        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
        dialog.showAndWait();
    }

    private void showReservationDialog(Vehicule v) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/view/reserver_louer.fxml"));
            javafx.scene.Parent root = loader.load();
            ReserverLouerController controller = loader.getController();
            controller.setVehicule(v);
            Stage stage = new Stage();
            stage.setTitle("Réserver / Louer");
            stage.setScene(new Scene(root));
            stage.showAndWait();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showHistoriqueDialog(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Historique des Locations");
        VBox content = new VBox(16);
        content.setStyle("-fx-padding: 24; -fx-background-color: #fff; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.10), 10, 0, 0, 2);");
        content.setMinWidth(440);
        content.setMinHeight(340);
        Label header = new Label("\uD83D\uDCC3  Historique pour " + v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-padding: 0 0 8 0;");
        java.util.List<model.Location> data = new dao.LocationDAO().findByVehiculeId(v.getId());
        if (data.isEmpty()) {
            VBox emptyBox = new VBox(10);
            emptyBox.setStyle("-fx-alignment: center; -fx-padding: 40;");
            Label icon = new Label("\uD83D\uDCC3");
            icon.setStyle("-fx-font-size: 54px; -fx-text-fill: #bdbdbd;");
            Label msg = new Label("Aucune location trouvée pour ce véhicule.");
            msg.setStyle("-fx-font-size: 16px; -fx-text-fill: #64748b; -fx-font-weight: bold;");
            emptyBox.getChildren().addAll(icon, msg);
            content.getChildren().addAll(header, emptyBox);
        } else {
            TableView<model.Location> table = new TableView<>();
            TableColumn<model.Location, String> clientCol = new TableColumn<>("Client");
            clientCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getClient() != null ? cellData.getValue().getClient().getNom() + " " + cellData.getValue().getClient().getPrenom() : ""
            ));
            TableColumn<model.Location, String> dateDebutCol = new TableColumn<>("Début");
            dateDebutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateDebut() != null ? cellData.getValue().getDateDebut().toString() : ""
            ));
            TableColumn<model.Location, String> dateFinCol = new TableColumn<>("Fin prévue");
            dateFinCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinPrevue() != null ? cellData.getValue().getDateFinPrevue().toString() : ""
            ));
            TableColumn<model.Location, String> statutCol = new TableColumn<>("Statut");
            statutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinReelle() == null ? "En cours" : "Terminée"
            ));
            // Color status
            statutCol.setCellFactory(col -> new javafx.scene.control.TableCell<>() {
                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setStyle("");
                    } else {
                        setText(item);
                        if (item.equals("En cours")) setStyle("-fx-text-fill: #f59e42; -fx-font-weight: bold;");
                        else if (item.equals("Terminée")) setStyle("-fx-text-fill: #64748b;");
                        else setStyle("-fx-text-fill: #10b981; -fx-font-weight: bold;");
                    }
                }
            });
            table.getColumns().addAll(clientCol, dateDebutCol, dateFinCol, statutCol);
            table.setItems(javafx.collections.FXCollections.observableArrayList(data));
            table.setMinHeight(220);
            table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
            content.getChildren().addAll(header, table);
        }
        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
        dialog.showAndWait();
    }

    private void showDisponibiliteDialog(Vehicule v) {
        Dialog<Void> dialog = new Dialog<>();
        dialog.setTitle("Disponibilité du Véhicule");
        VBox content = new VBox(16);
        content.setStyle("-fx-padding: 24; -fx-background-color: #fff; -fx-border-color: #e2e8f0; -fx-border-radius: 12; -fx-background-radius: 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.10), 10, 0, 0, 2);");
        content.setMinWidth(440);
        content.setMinHeight(340);
        Label header = new Label("\uD83D\uDE97  Disponibilité pour " + v.getMarque() + " " + v.getModele());
        header.setStyle("-fx-font-size: 22px; -fx-font-weight: bold; -fx-text-fill: #1a3c40; -fx-padding: 0 0 8 0;");
        java.util.List<model.Location> data = new dao.LocationDAO().findActiveByVehiculeId(v.getId());
        if (data.isEmpty()) {
            VBox emptyBox = new VBox(10);
            emptyBox.setStyle("-fx-alignment: center; -fx-padding: 40;");
            Label icon = new Label("\uD83D\uDE97");
            icon.setStyle("-fx-font-size: 54px; -fx-text-fill: #bdbdbd;");
            Label msg = new Label("Aucune réservation ou location active pour ce véhicule.");
            msg.setStyle("-fx-font-size: 16px; -fx-text-fill: #64748b; -fx-font-weight: bold;");
            emptyBox.getChildren().addAll(icon, msg);
            content.getChildren().addAll(header, emptyBox);
        } else {
            TableView<model.Location> table = new TableView<>();
            TableColumn<model.Location, String> dateDebutCol = new TableColumn<>("Début");
            dateDebutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateDebut() != null ? cellData.getValue().getDateDebut().toString() : ""
            ));
            TableColumn<model.Location, String> dateFinCol = new TableColumn<>("Fin prévue");
            dateFinCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinPrevue() != null ? cellData.getValue().getDateFinPrevue().toString() : ""
            ));
            TableColumn<model.Location, String> statutCol = new TableColumn<>("Statut");
            statutCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getDateFinReelle() == null ? "Réservé" : "Disponible"
            ));
            // Color status
            statutCol.setCellFactory(col -> new javafx.scene.control.TableCell<>() {
                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setStyle("");
                    } else {
                        setText(item);
                        if (item.equals("Réservé")) setStyle("-fx-text-fill: #f59e42; -fx-font-weight: bold;");
                        else if (item.equals("Disponible")) setStyle("-fx-text-fill: #10b981; -fx-font-weight: bold;");
                        else setStyle("-fx-text-fill: #64748b;");
                    }
                }
            });
            table.getColumns().addAll(dateDebutCol, dateFinCol, statutCol);
            table.setItems(javafx.collections.FXCollections.observableArrayList(data));
            table.setMinHeight(220);
            table.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
            content.getChildren().addAll(header, table);
        }
        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
        dialog.showAndWait();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) cataloguePane.getScene().getWindow());
    }

    @FXML
    private void handleRefresh() {
        loadCatalogue();
    }

    @FXML
    private void handleExport() {
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("Exporter le catalogue");
        fileChooser.getExtensionFilters().add(new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        java.io.File file = fileChooser.showSaveDialog(cataloguePane.getScene().getWindow());
        if (file != null) {
            try {
                String[] headers = {"Marque", "Modele", "Immatriculation", "Etat", "PrixParJour", "Carburant", "Metrage", "DateAcquisition", "LastUsed", "NbreChevaux", "AssuranceCompagnie", "AssuranceExpiration", "AssuranceNumero"};
                java.util.List<model.Vehicule> vehicules = vehiculeDAO.findAll();
                util.ExportUtil.exportToCSV(vehicules, headers, v -> new String[] {
                    v.getMarque(), v.getModele(), v.getImmatriculation(), v.getEtat(),
                    String.valueOf(v.getPrixParJour()),
                    v.getCarburant(), String.valueOf(v.getMetrage()),
                    v.getDateAcquisition() != null ? v.getDateAcquisition().toString() : "",
                    v.getLastUsed() != null ? v.getLastUsed().toString() : "",
                    v.getNbreChevaux() != null ? v.getNbreChevaux().toString() : "",
                    v.getAssuranceCompagnie(),
                    v.getAssuranceExpiration() != null ? v.getAssuranceExpiration().toString() : "",
                    v.getAssuranceNumero()
                }, file.getAbsolutePath());
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
                alert.setTitle("Export réussi");
                alert.setHeaderText(null);
                alert.setContentText("Catalogue exporté avec succès vers :\n" + file.getAbsolutePath());
                alert.showAndWait();
            } catch (Exception e) {
                javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
                alert.setTitle("Erreur d'export");
                alert.setHeaderText(null);
                alert.setContentText("Erreur lors de l'export : " + e.getMessage());
                alert.showAndWait();
            }
        }
    }

    @FXML
    private void handleRechercher() {
        handleApplyFilters();
    }

    @FXML
    private void handleFilter(ActionEvent actionEvent) {
        handleApplyFilters();
    }

    @FXML
    private void handleApplyFilters() {
        String marque = marqueFilter != null ? marqueFilter.getValue() : null;
        String etat = etatFilter != null ? etatFilter.getValue() : null;
        String prixText = prixFilter != null ? prixFilter.getText() : null;
        String keyword = searchField != null ? searchField.getText().toLowerCase() : "";
        Double prixMax = null;
        try {
            if (prixText != null && !prixText.isBlank()) {
                prixMax = Double.parseDouble(prixText);
            }
        } catch (NumberFormatException ignored) {}
        Double finalPrixMax = prixMax;
        List<Vehicule> filtered = vehiculeDAO.findAll().stream()
            .filter(v -> (marque == null || marque.isBlank() || v.getMarque().equalsIgnoreCase(marque)))
            .filter(v -> (etat == null || etat.isBlank() || v.getEtat().equalsIgnoreCase(etat)))
            .filter(v -> {
                if (finalPrixMax == null) return true;
                Double prix = v.getPrixParJour();
                // Fix: If prix is null, exclude from results when filtering by price
                if (prix == null) return false;
                return prix <= finalPrixMax;
            })
            .filter(v -> keyword.isBlank() || v.getMarque().toLowerCase().contains(keyword)
                || v.getModele().toLowerCase().contains(keyword)
                || v.getImmatriculation().toLowerCase().contains(keyword))
            .toList();
        updateCatalogue(filtered);
    }

    @FXML
    private void handleClearFilters() {
        if (marqueFilter != null) marqueFilter.getSelectionModel().clearSelection();
        if (etatFilter != null) etatFilter.getSelectionModel().clearSelection();
        if (prixFilter != null) prixFilter.clear();
        if (searchField != null) searchField.clear();
        updateCatalogue(vehiculeDAO.findAll());
    }

    private void updateCatalogue(List<Vehicule> vehicules) {
        cataloguePane.getChildren().clear();
        for (Vehicule v : vehicules) {
            HBox card = createVehicleCard(v);
            cataloguePane.getChildren().add(card);
        }
        if (lblTotalCount != null) {
            lblTotalCount.setText("Total: " + vehicules.size() + " véhicules");
        }
    }

    /**
     * Loads the location_create.fxml as the main content in the dashboard, with the given vehicle preselected.
     */
    private void openLocationCreateWithVehicle(Vehicule vehicule) {
        try {
            // Find the dashboard's contentPane
            javafx.scene.Scene scene = cataloguePane.getScene();
            javafx.scene.Node node = scene.lookup("#contentPane");
            if (node instanceof javafx.scene.layout.StackPane contentPane) {
                javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location_create.fxml"));
                javafx.scene.Parent locationRoot = loader.load();
                // Set the vehicle in the controller
                controller.LocationCreateController controller = loader.getController();
                controller.setVehicule(vehicule);
                contentPane.getChildren().setAll(locationRoot);
            } else {
                // Fallback: open as a new window if not in dashboard
                javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/location_create.fxml"));
                javafx.scene.Parent root = loader.load();
                controller.LocationCreateController controller = loader.getController();
                controller.setVehicule(vehicule);
                javafx.stage.Stage stage = new javafx.stage.Stage();
                stage.setTitle("Nouvelle Location / Réservation");
                stage.setScene(new javafx.scene.Scene(root));
                stage.setMaximized(true);
                stage.show();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
