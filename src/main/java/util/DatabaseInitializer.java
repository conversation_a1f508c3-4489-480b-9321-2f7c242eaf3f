package util;

import dao.AdminDAO;
import dao.AgentDAO;
import dao.ClientDAO;
import dao.VehiculeDAO;
import model.Admin;
import model.Agent;
import model.Client;
import model.Vehicule;
import org.mindrot.jbcrypt.BCrypt;
import dao.LocationDAO;
import dao.PaiementDAO;
import model.Location;
import model.Paiement;

public class DatabaseInitializer {
    public static void initialize() {
        AdminDAO adminDAO = new AdminDAO();
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        ClientDAO clientDAO = new ClientDAO();

        // Admin
        if (adminDAO.findAll().isEmpty()) {
            Admin admin = new Admin();
            admin.setUsername("admin");
            admin.setPasswordHash(BCrypt.hashpw("admin123", BCrypt.gensalt()));
            admin.setRole("admin");
            admin.setEmail("<EMAIL>");
            admin.setStatus("ACTIF");
            adminDAO.save(admin);
        }

        // Véhicules
        if (vehiculeDAO.findAll().isEmpty()) {
            for (int i = 1; i <= 15; i++) {
                Vehicule v = new Vehicule();
                v.setMarque(i % 3 == 0 ? "Renault" : i % 3 == 1 ? "Peugeot" : "Dacia");
                v.setModele("Model" + i);
                v.setImmatriculation(String.format("%03d-XYZ-%02d", i * 7, i));
                v.setEtat(i % 5 == 0 ? "en panne" : i % 4 == 0 ? "loué" : "disponible");
                v.setPrixParJour(30 + i);
                v.setPhotoUrl("images/car" + i + ".jpg");
                v.setCarburant(i % 4 == 0 ? "Diesel" : i % 3 == 0 ? "Essence" : "Hybride");
                v.setMetrage(40000 + i * 1000);
                v.setDateAcquisition(java.time.LocalDate.now().minusMonths(i * 2));
                v.setLastUsed(java.time.LocalDate.now().minusDays(i));
                v.setNbreChevaux(4 + (i % 4));
                v.setAssuranceCompagnie(i % 2 == 0 ? "AXA" : "Wafa Assurance");
                v.setAssuranceExpiration(java.time.LocalDate.now().plusMonths(i));
                v.setAssuranceNumero("ASSUR-" + i);
                vehiculeDAO.save(v);
            }
        }
        // Clients
        if (clientDAO.findAll().isEmpty()) {
            for (int i = 1; i <= 10; i++) {
                Client c = new Client();
                c.setNom("ClientNom" + i);
                c.setPrenom("Prenom" + i);
                c.setCin("CIN" + (10000 + i));
                c.setTelephone("06000000" + String.format("%02d", i));
                c.setEmail("client" + i + "@email.com");
                clientDAO.save(c);
            }
        }
        // Locations + Paiements
        LocationDAO locationDAO = new LocationDAO();
        PaiementDAO paiementDAO = new PaiementDAO();
        if (locationDAO.findAll().isEmpty()) {
            java.util.List<Client> clients = clientDAO.findAll();
            java.util.List<Vehicule> vehicules = vehiculeDAO.findAll();
            java.util.Random rand = new java.util.Random();
            for (int i = 0; i < 20; i++) {
                Location loc = new Location();
                Client client = clients.get(rand.nextInt(clients.size()));
                Vehicule vehicule = vehicules.get(rand.nextInt(vehicules.size()));
                loc.setClient(client);
                loc.setVehicule(vehicule);
                java.time.LocalDate start = java.time.LocalDate.now().minusDays(rand.nextInt(365));
                loc.setDateDebut(start);
                loc.setDateFinPrevue(start.plusDays(3 + rand.nextInt(7)));
                boolean finished = rand.nextBoolean();
                if (finished) {
                    loc.setDateFinReelle(loc.getDateFinPrevue().plusDays(rand.nextInt(3)));
                }
                double prix = vehicule.getPrixParJour() * (loc.getDateFinPrevue().toEpochDay() - loc.getDateDebut().toEpochDay());
                loc.setPrixTotal(prix);
                loc.setPenalite(finished && rand.nextBoolean() ? 20.0 : 0.0);
                locationDAO.save(loc);

                // Add a paiement for some locations
                if (rand.nextBoolean()) {
                    // Get the saved location from database to ensure it's managed by Hibernate
                    java.util.List<Location> savedLocations = locationDAO.findAll();
                    Location savedLocation = savedLocations.get(savedLocations.size() - 1); // Get the last saved location

                    Paiement p = new Paiement();
                    p.setLocation(savedLocation);
                    p.setMontant(prix + (savedLocation.getPenalite() != 0 ? savedLocation.getPenalite() : 0));
                    p.setDatePaiement(savedLocation.getDateFinReelle() != null ? savedLocation.getDateFinReelle() : savedLocation.getDateFinPrevue());
                    paiementDAO.save(p);
                }
            }
        }

        // Agent
        AgentDAO agentDAO = new AgentDAO();
        if (agentDAO.findAll().isEmpty()) {
            Agent agent = new Agent();
            agent.setUsername("agent1");
            agent.setPasswordHash(BCrypt.hashpw("agent123", BCrypt.gensalt()));
            agent.setRole("agent");
            agent.setEmail("<EMAIL>");
            agent.setStatus("ACTIF");
            agentDAO.save(agent);
        }
    }
    public static void main(String[] args) {
        initialize();
    }
} 