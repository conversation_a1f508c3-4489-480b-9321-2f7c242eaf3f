package util;

import javafx.geometry.Rectangle2D;
import javafx.stage.Screen;
import javafx.stage.Stage;

/**
 * Utility class for window management operations
 */
public class WindowUtil {
    
    /**
     * Maximizes a window properly to avoid taskbar overlap
     * Uses visual bounds instead of screen bounds to respect taskbar
     * 
     * @param stage The stage to maximize
     */
    public static void maximizeWindow(Stage stage) {
        try {
            // Get screen bounds excluding taskbar and other system UI
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            Rectangle2D screenBounds = screen.getBounds();

            // Calculate taskbar height (difference between screen and visual bounds)
            double taskbarHeight = screenBounds.getHeight() - visualBounds.getHeight();

            // Add extra padding to ensure no overlap (minimum 50px, or taskbar height + 20px)
            double extraPadding = Math.max(50, taskbarHeight + 20);

            // Set window position and size with extra padding for taskbar
            stage.setX(visualBounds.getMinX());
            stage.setY(visualBounds.getMinY());
            stage.setWidth(visualBounds.getWidth());
            stage.setHeight(visualBounds.getHeight() - extraPadding);

            // Ensure window is not decorated as maximized (to avoid OS-level maximization issues)
            stage.setMaximized(false);

            System.out.println("Window maximized with visual bounds: " + visualBounds);
            System.out.println("Taskbar height detected: " + taskbarHeight + "px");
            System.out.println("Extra padding applied: " + extraPadding + "px");

        } catch (Exception e) {
            // Fallback to regular maximized if screen bounds fail
            System.err.println("Failed to maximize window with visual bounds, using fallback: " + e.getMessage());
            stage.setMaximized(true);
        }
    }
    
    /**
     * Centers a window on screen
     * 
     * @param stage The stage to center
     */
    public static void centerWindow(Stage stage) {
        try {
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            
            double centerX = visualBounds.getMinX() + (visualBounds.getWidth() - stage.getWidth()) / 2;
            double centerY = visualBounds.getMinY() + (visualBounds.getHeight() - stage.getHeight()) / 2;
            
            stage.setX(centerX);
            stage.setY(centerY);
        } catch (Exception e) {
            // Fallback to JavaFX built-in centering
            stage.centerOnScreen();
        }
    }
    
    /**
     * Maximizes window using a conservative percentage approach
     * Uses 95% of screen height to ensure no taskbar overlap
     *
     * @param stage The stage to maximize
     */
    public static void maximizeWindowConservative(Stage stage) {
        try {
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();

            // Use 95% of available height to be extra safe
            double safeHeight = visualBounds.getHeight() * 0.95;

            stage.setX(visualBounds.getMinX());
            stage.setY(visualBounds.getMinY());
            stage.setWidth(visualBounds.getWidth());
            stage.setHeight(safeHeight);
            stage.setMaximized(false);

            System.out.println("Conservative maximization: " + visualBounds.getWidth() + "x" + safeHeight);

        } catch (Exception e) {
            System.err.println("Conservative maximization failed, using fallback: " + e.getMessage());
            stage.setMaximized(true);
        }
    }

    /**
     * Sets window to a specific size and centers it
     *
     * @param stage The stage to resize and center
     * @param width The desired width
     * @param height The desired height
     */
    public static void setWindowSizeAndCenter(Stage stage, double width, double height) {
        stage.setWidth(width);
        stage.setHeight(height);
        centerWindow(stage);
    }
}
