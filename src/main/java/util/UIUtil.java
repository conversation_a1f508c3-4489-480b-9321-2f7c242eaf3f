package util;

import javafx.stage.Stage;
import javafx.geometry.Rectangle2D;
import javafx.stage.Screen;

/**
 * Utility class for UI operations and window management
 */
public class UIUtil {
    
    /**
     * Ensures proper window sizing with taskbar consideration
     * Sets window to 95% of screen height to avoid taskbar overlap
     *
     * @param stage The stage to configure
     */
    public static void ensureProperWindowSize(Stage stage) {
        try {
            // Get screen dimensions
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();

            // Set window to use 95% of available height to avoid taskbar issues
            double safeHeight = visualBounds.getHeight() * 0.95;
            double safeWidth = visualBounds.getWidth() * 0.98;

            // Position window properly
            stage.setX(visualBounds.getMinX() + (visualBounds.getWidth() - safeWidth) / 2);
            stage.setY(visualBounds.getMinY());
            stage.setWidth(safeWidth);
            stage.setHeight(safeHeight);

            // Ensure window is not maximized to avoid OS-level issues
            stage.setMaximized(false);

        } catch (Exception e) {
            // Fallback to standard maximization
            stage.setMaximized(true);
        }
    }
    
    /**
     * Centers a stage on screen
     * 
     * @param stage The stage to center
     */
    public static void centerStage(Stage stage) {
        try {
            Screen screen = Screen.getPrimary();
            Rectangle2D visualBounds = screen.getVisualBounds();
            
            double centerX = visualBounds.getMinX() + (visualBounds.getWidth() - stage.getWidth()) / 2;
            double centerY = visualBounds.getMinY() + (visualBounds.getHeight() - stage.getHeight()) / 2;
            
            stage.setX(centerX);
            stage.setY(centerY);
        } catch (Exception e) {
            stage.centerOnScreen();
        }
    }
}
